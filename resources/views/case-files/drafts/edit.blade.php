<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-base-content leading-tight">
                {{ __('app.edit_draft') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('case-files.drafts.show', [$caseFile, $draft]) }}" class="btn btn-ghost btn-sm">
                    ← {{ __('app.back_to_draft') }}
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-base-100 overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    <form action="{{ route('case-files.drafts.update', [$caseFile, $draft]) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="mb-6">
                            <label for="draft_type" class="block text-sm font-medium text-base-content mb-2">
                                {{ __('app.document_type') }} <span class="text-error">*</span>
                            </label>
                            <select id="draft_type" name="draft_type"
                                class="select select-bordered w-full @error('draft_type') select-error @enderror"
                                required>
                                <option value="complaint" {{ $draft->draft_type === 'complaint' ? 'selected' : '' }}>
                                    {{ __('app.complaint') }}</option>
                                <option value="motion" {{ $draft->draft_type === 'motion' ? 'selected' : '' }}>
                                    {{ __('app.motion') }}</option>
                                <option value="affidavit" {{ $draft->draft_type === 'affidavit' ? 'selected' : '' }}>
                                    {{ __('app.affidavit') }}</option>
                                <option value="proposed_order" {{ $draft->draft_type === 'proposed_order' ? 'selected' : '' }}>
                                    {{ __('app.proposed_order') }}</option>
                                <option value="certificate_of_service" {{ $draft->draft_type === 'certificate_of_service' ? 'selected' : '' }}>
                                    {{ __('app.certificate_of_service') }}</option>
                                <option value="letter" {{ $draft->draft_type === 'letter' ? 'selected' : '' }}>
                                    {{ __('app.letter') }}</option>
                                <option value="contract" {{ $draft->draft_type === 'contract' ? 'selected' : '' }}>
                                    {{ __('app.contract') }}</option>
                            </select>
                            @error('draft_type')
                                <p class="mt-1 text-sm text-error">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-6">
                            <label for="description" class="block text-sm font-medium text-base-content mb-2">
                                {{ __('app.description') }}
                            </label>
                            <textarea id="description" name="description" rows="3"
                                class="textarea textarea-bordered w-full @error('description') textarea-error @enderror"
                                placeholder="{{ __('app.brief_description') }}">{{ old('description', $draft->description) }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-error">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-6">
                            <label for="status" class="block text-sm font-medium text-base-content mb-2">
                                {{ __('app.status') }}
                            </label>
                            <select id="status" name="status"
                                class="select select-bordered w-full @error('status') select-error @enderror">
                                <option value="draft" {{ $draft->status === 'draft' ? 'selected' : '' }}>
                                    {{ __('app.draft') }}</option>
                                <option value="review" {{ $draft->status === 'review' ? 'selected' : '' }}>
                                    {{ __('app.review') }}</option>
                                <option value="published" {{ $draft->status === 'published' ? 'selected' : '' }}>
                                    {{ __('app.published') }}</option>
                            </select>
                            @error('status')
                                <p class="mt-1 text-sm text-error">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-6">
                            <label for="strategy_id" class="block text-sm font-medium text-base-content mb-2">
                                {{ __('Apply Case Strategy') }}
                            </label>
                            @php
                                $strategies = $caseFile->strategies()->latest()->get();
                                $currentStrategyId = isset($draft->metadata['strategy_id']) ? $draft->metadata['strategy_id'] : null;
                            @endphp

                            @if($strategies->count() > 0)
                                <select id="strategy_id" name="strategy_id" class="select select-bordered w-full">
                                    <option value="">{{ __('None - No strategy applied') }}</option>
                                    @foreach($strategies as $strategy)
                                        <option value="{{ $strategy->id }}" {{ $currentStrategyId == $strategy->id ? 'selected' : '' }}>
                                            {{ $strategy->title ?: 'Strategy ' . $strategy->version }}
                                        </option>
                                    @endforeach
                                </select>
                                <p class="mt-1 text-xs text-base-content/70">{{ __('Applying a strategy will incorporate its recommendations into the document draft.') }}</p>
                            @else
                                <div class="alert alert-info">
                                    <div>
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current flex-shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                                        <div>
                                            <p class="font-medium">{{ __('No strategies available') }}</p>
                                            <p class="text-sm">{{ __('Create a case strategy first to apply it to your document.') }}</p>
                                        </div>
                                    </div>
                                    <div class="flex-none">
                                        <a href="{{ route('case-files.strategy.create', $caseFile) }}" class="btn btn-sm btn-primary">
                                            {{ __('Create Strategy') }}
                                        </a>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <div class="flex justify-end space-x-2">
                            <a href="{{ route('case-files.drafts.show', [$caseFile, $draft]) }}" class="btn btn-ghost">
                                {{ __('app.cancel') }}
                            </a>
                            <button type="submit" class="btn btn-primary">
                                {{ __('app.update_draft') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
