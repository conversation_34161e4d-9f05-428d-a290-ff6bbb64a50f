<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-base-content leading-tight">
                {{ $template->name }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('document-templates.index') }}" class="btn btn-ghost btn-sm">
                    ← {{ __('Back to Templates') }}
                </a>
                <a href="{{ route('document-templates.edit', $template) }}" class="btn btn-primary btn-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    {{ __('Edit Template') }}
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Template Details Card -->
            <div class="bg-base-100 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="text-lg font-medium text-base-content">Template Details</h3>
                            <p class="text-sm text-base-content/70 mt-1">{{ $template->description ?? 'No description provided' }}</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="badge {{ $template->is_active ? 'badge-success' : 'badge-ghost' }}">
                                {{ $template->is_active ? 'Active' : 'Inactive' }}
                            </span>
                            <span class="badge badge-primary capitalize">
                                {{ $template->document_type }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="divider"></div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h4 class="text-sm font-medium text-base-content/70">Created</h4>
                            <p>{{ $template->created_at->format('M d, Y g:i A') }}</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-base-content/70">Last Updated</h4>
                            <p>{{ $template->updated_at->format('M d, Y g:i A') }}</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-base-content/70">Created By</h4>
                            <p>{{ $template->creator ? $template->creator->name : 'Unknown' }}</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-base-content/70">Usage</h4>
                            <p>{{ $template->drafts()->count() }} drafts created from this template</p>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <button class="btn btn-primary" onclick="document.getElementById('create-draft-modal').showModal()">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            Create Draft from Template
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Template Structure Card -->
            <div class="bg-base-100 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-base-content mb-4">Document Structure</h3>
                    
                    <div class="overflow-x-auto">
                        <table class="table w-full">
                            <thead>
                                <tr>
                                    <th>Order</th>
                                    <th>Section</th>
                                    <th>Required</th>
                                    <th>AI Prompt</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach(json_decode($template->structure, true) as $section)
                                    <tr class="hover">
                                        <td>{{ $section['order'] ?? '-' }}</td>
                                        <td class="font-medium">{{ $section['name'] }}</td>
                                        <td>
                                            @if(isset($section['required']) && $section['required'])
                                                <span class="badge badge-success">Required</span>
                                            @else
                                                <span class="badge badge-ghost">Optional</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if(isset(json_decode($template->ai_prompts, true)[$section['id']]))
                                                <span class="text-xs">{{ Str::limit(json_decode($template->ai_prompts, true)[$section['id']], 50) }}</span>
                                            @else
                                                <span class="text-xs text-base-content/50">No prompt defined</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Template JSON Data Card -->
            <div class="bg-base-100 overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-base-content mb-4">Template Data</h3>
                    
                    <div class="tabs tabs-boxed mb-4">
                        <a class="tab tab-active" onclick="showTab('structure')">Structure</a>
                        <a class="tab" onclick="showTab('default-content')">Default Content</a>
                        <a class="tab" onclick="showTab('ai-prompts')">AI Prompts</a>
                        <a class="tab" onclick="showTab('metadata')">Metadata</a>
                    </div>
                    
                    <div id="structure-tab" class="tab-content">
                        <pre class="bg-base-200 p-4 rounded-lg overflow-x-auto text-xs">{{ json_encode(json_decode($template->structure), JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) }}</pre>
                    </div>
                    
                    <div id="default-content-tab" class="tab-content hidden">
                        @if($template->default_content)
                            <pre class="bg-base-200 p-4 rounded-lg overflow-x-auto text-xs">{{ json_encode(json_decode($template->default_content), JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) }}</pre>
                        @else
                            <div class="text-center py-8 text-base-content/50">
                                <p>No default content defined</p>
                            </div>
                        @endif
                    </div>
                    
                    <div id="ai-prompts-tab" class="tab-content hidden">
                        @if($template->ai_prompts)
                            <pre class="bg-base-200 p-4 rounded-lg overflow-x-auto text-xs">{{ json_encode(json_decode($template->ai_prompts), JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) }}</pre>
                        @else
                            <div class="text-center py-8 text-base-content/50">
                                <p>No AI prompts defined</p>
                            </div>
                        @endif
                    </div>
                    
                    <div id="metadata-tab" class="tab-content hidden">
                        @if($template->metadata)
                            <pre class="bg-base-200 p-4 rounded-lg overflow-x-auto text-xs">{{ json_encode(json_decode($template->metadata), JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) }}</pre>
                        @else
                            <div class="text-center py-8 text-base-content/50">
                                <p>No metadata defined</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Create Draft Modal -->
    <dialog id="create-draft-modal" class="modal">
        <div class="modal-box">
            <h3 class="font-bold text-lg">Create Draft from Template</h3>
            <p class="py-4">Select a case file to create a new draft using this template.</p>
            
            <form action="{{ route('document-templates.create-draft', $template) }}" method="POST">
                @csrf
                
                <div class="mb-4">
                    <label for="case_file_id" class="block text-sm font-medium text-base-content mb-2">
                        {{ __('Case File') }} <span class="text-error">*</span>
                    </label>
                    <select id="case_file_id" name="case_file_id" class="select select-bordered w-full" required>
                        <option value="" disabled selected>{{ __('Select a case file') }}</option>
                        @foreach(\App\Models\CaseFile::orderBy('title')->get() as $caseFile)
                            <option value="{{ $caseFile->id }}">{{ $caseFile->title }}</option>
                        @endforeach
                    </select>
                </div>
                
                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium text-base-content mb-2">
                        {{ __('Draft Description') }}
                    </label>
                    <textarea id="description" name="description" rows="2" class="textarea textarea-bordered w-full" placeholder="Brief description of this draft">{{ $template->name }}</textarea>
                </div>
                
                <div class="modal-action">
                    <button type="button" class="btn" onclick="document.getElementById('create-draft-modal').close()">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Draft</button>
                </div>
            </form>
        </div>
    </dialog>
    
    <script>
        function showTab(tabId) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('tab-active');
            });
            
            // Show the selected tab content
            document.getElementById(tabId + '-tab').classList.remove('hidden');
            
            // Add active class to the clicked tab
            event.target.classList.add('tab-active');
        }
    </script>
</x-app-layout>
