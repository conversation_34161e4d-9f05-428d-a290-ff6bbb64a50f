<x-app-layout>
    @push('scripts')
        <script>
            // Check if we need to open the create case modal
            document.addEventListener('DOMContentLoaded', function() {
                @if (session('openCreateCaseModal'))
                    window.dispatchEvent(new CustomEvent('open-create-case-modal'));
                @endif
            });

            function confirmDelete(caseId) {
                Swal.fire({
                    title: '{{ __('Delete Case File?') }}',
                    text: '{{ __('Are you sure you want to delete this case file? This action cannot be undone.') }}',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#EF4444',
                    cancelButtonColor: '#6B7280',
                    confirmButtonText: '{{ __('Yes, delete it!') }}',
                    cancelButtonText: '{{ __('Cancel') }}'
                }).then((result) => {
                    if (result.isConfirmed) {
                        fetch(`/case-files/${caseId}`, {
                            method: 'DELETE',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                                'Accept': 'application/json',
                            }
                        }).then(response => {
                            if (response.ok) {
                                window.location.reload();
                            }
                        });
                    }
                });
            }
        </script>
    @endpush
@if(!auth()->user()->email == "<EMAIL>")
    <!-- Waiting List Overlay - Un-closable -->
    <div class="fixed inset-0 z-50 flex items-center justify-center bg-base-100/80 backdrop-blur-sm">
        <div class="max-w-md p-6 text-center shadow-2xl card bg-base-200">
            <div class="mb-4 card-body">
                <div class="flex justify-center mb-6">
                    <div class="p-4 rounded-full bg-primary/10">
                        <svg class="w-12 h-12 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
                <h2 class="mb-4 text-2xl font-bold text-center">{{ __('You\'re on the Waiting List') }}</h2>
                <p class="mb-6 text-base-content/70">
                    {{ __('Thank you for your interest! Your account is currently on our waiting list but you\'ll be able to test in about a week\'s time. We\'ll notify you as soon as you\'re granted access to the full platform.') }}
                </p>
                <form method="POST" action="{{ route('logout') }}" class="justify-center card-actions">
                    @csrf
                    <button type="submit" class="btn btn-outline">{{ __('Log Out') }}</button>
                </form>
            </div>
        </div>
    </div>
@endif
    <x-slot name="header">
        <div class="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
            <h2 class="flex items-center gap-2 text-xl font-semibold leading-tight text-base-content">
                <span class="text-2xl">💼</span>
                {{ __('dashboard.recent_cases') }}
            </h2>
            <button class="w-full btn btn-primary btn-sm sm:w-auto"
                @click="window.dispatchEvent(new CustomEvent('open-create-case-modal'))">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                {{ __('app.new_case') }}
            </button>
        </div>
    </x-slot>

    <livewire:create-case-modal />

    <div class="py-6 sm:py-12">
        <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
            <!-- Search Form -->
            <div class="mb-6">
                <form method="GET" action="{{ route('dashboard') }}" class="flex gap-2">
                    <div class="flex-1">
                        <div class="relative">
                            <input type="text" name="search" value="{{ $search }}"
                                class="w-full input input-bordered" placeholder="{{ __('Search cases...') }}">
                        </div>
                    </div>
                </form>
            </div>

            @if ($caseFiles->count() > 0)
                <div class="grid gap-6">
                    @foreach ($caseFiles as $caseFile)
                        <div
                            class="overflow-hidden transition-all duration-300 transform border-l-4 rounded-lg shadow-xl card bg-base-100 hover:shadow-2xl border-primary hover:-translate-y-1">
                            <div class="p-6 card-body">
                                <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                                    <div class="flex-1">
                                        <div class="flex flex-wrap items-center gap-3 mb-3">
                                            <h3
                                                class="flex items-center text-xl font-bold break-words card-title text-base-content">
                                                <div class="p-2 mr-3 rounded-lg bg-primary/10">
                                                    <svg class="w-6 h-6 text-primary" fill="none"
                                                        stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9h6m-3 3h-3">
                                                        </path>
                                                    </svg>
                                                </div>
                                                {{ $caseFile->title }}
                                            </h3>
                                        </div>

                                        <div class="flex flex-wrap items-center gap-2 mb-3">
                                            {{-- Badges with enhanced styles --}}
                                            @if ($caseFile->openai_assistant_id && $caseFile->status !== 'setup_failed')
                                                <div class="gap-1 py-3 badge badge-success badge-outline">
                                                    <svg class="w-3 h-3" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                    {{ __('app.assistant_ready') }}
                                                </div>
                                            @else
                                                <div class="gap-1 py-3 badge badge-warning">
                                                    <svg class="w-3 h-3" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                                    </svg>
                                                    {{ __('app.assistant_pending') }}
                                                </div>
                                            @endif

                                            @if ($caseFile->collaboration_enabled)
                                                <div class="gap-1 py-3 badge badge-info badge-outline">
                                                    <svg class="w-3 h-3" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                                    </svg>
                                                    @php
                                                        $activeCollaborators = $caseFile
                                                            ->collaborators()
                                                            ->where('status', 'active')
                                                            ->count();
                                                    @endphp
                                                    {{ $activeCollaborators ? "$activeCollaborators " . __('app.collaborators') : __('app.collaboration_enabled') }}
                                                </div>
                                            @endif
                                        </div>

                                        {{-- Add case metadata --}}
                                        <p class="mt-1 text-sm text-base-content/70">
                                            <span class="inline-flex items-center mr-3">
                                                <svg class="w-4 h-4 mr-1 text-base-content/50" fill="none"
                                                    stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                                    </path>
                                                </svg>
                                                {{ $caseFile->created_at->format('M d, Y') }}
                                            </span>
                                            @if ($caseFile->updated_at->ne($caseFile->created_at))
                                                <span class="inline-flex items-center">
                                                    <svg class="w-4 h-4 mr-1 text-base-content/50" fill="none"
                                                        stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    {{ __('Updated') }}: {{ $caseFile->updated_at->diffForHumans() }}
                                                </span>
                                            @endif
                                        </p>
                                    </div>
                                    <div class="flex flex-wrap items-center gap-3 mt-2 sm:mt-0">
                                        <a href="{{ route('case-files.show', $caseFile) }}"
                                            class="btn btn-primary btn-sm">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                                </path>
                                            </svg> {{ __('app.view_case') }}
                                        </a>
                                        <button onclick="confirmDelete({{ $caseFile->id }})"
                                            class="btn btn-outline btn-error btn-sm">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                                </path>
                                            </svg> {{ __('app.delete') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="py-12 text-center">
                    <div class="shadow-xl card bg-base-100">
                        <div class="card-body">
                            <p class="text-base-content/70">{{ __('dashboard.no_cases') }}</p>
                            <button class="w-full btn btn-primary btn-sm sm:w-auto"
                                @click="window.dispatchEvent(new CustomEvent('open-create-case-modal'))">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 4v16m8-8H4"></path>
                                </svg>
                                {{ __('app.new_case') }}
                            </button>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Pagination -->
            @if ($caseFiles->hasPages())
                <div class="mt-6">
                    {{ $caseFiles->links() }}
                </div>
            @endif
        </div>
    </div>


    <!-- Invoice Section -->
    <div class="py-12">
        <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
            <div class="p-8 shadow-xl bg-base-100/50 dark:bg-neutral-focus/50 backdrop-blur-xl sm:rounded-lg">
                <!-- Invoice Widgets -->
                <div>
                    <div
                        class="flex flex-col mb-8 space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                        <h2 class="flex items-center gap-2 text-2xl font-bold leading-tight text-base-content">
                            <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            {{ __('dashboard.invoices_and_billing') }}
                        </h2>
                        <div class="flex flex-wrap gap-2">
                            <a href="{{ route('invoices.received') }}"
                                class="w-full btn btn-outline btn-sm sm:w-auto">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                                </svg>
                                {{ __('dashboard.view_received') }}
                            </a>
                            <a href="{{ route('invoices.index') }}" class="w-full btn btn-outline btn-sm sm:w-auto">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                </svg>
                                {{ __('dashboard.view_sent') }}
                            </a>
                            <a href="{{ route('invoices.create') }}" class="w-full btn btn-primary btn-sm sm:w-auto">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 4v16m8-8H4"></path>
                                </svg>
                                {{ __('dashboard.create_invoice') }}
                            </a>
                        </div>
                    </div>

                    @if ($connectStatus !== 'active')
                        <div class="mb-6 shadow-xl card bg-base-200">
                            <div class="card-body">
                                <div class="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                                    <div class="flex items-start gap-4">
                                        <div class="p-3 rounded-lg bg-primary/10">
                                            <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 class="text-lg font-bold">{{ __('dashboard.setup_connect_account') }}
                                            </h4>
                                            <p class="mt-1 text-base-content/70">
                                                @if ($connectStatus === 'not_setup')
                                                    {{ __('dashboard.setup_description') }}
                                                @elseif($connectStatus === 'incomplete')
                                                    {{ __('dashboard.incomplete_description') }}
                                                @elseif($connectStatus === 'pending')
                                                    {{ __('dashboard.pending_description') }}
                                                @endif
                                            </p>
                                        </div>
                                    </div>
                                    <div>
                                        <a href="{{ route('connect.index') }}" class="btn btn-primary">
                                            @if ($connectStatus === 'not_setup')
                                                {{ __('dashboard.setup_connect') }}
                                            @elseif($connectStatus === 'incomplete')
                                                {{ __('dashboard.complete_setup') }}
                                            @elseif($connectStatus === 'pending')
                                                {{ __('dashboard.check_status') }}
                                            @endif
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                        <!-- Received Invoices Widget -->
                        <div class="transition-shadow duration-300 shadow-xl card bg-base-200 hover:shadow-2xl">
                            <div class="card-body">
                                <h3 class="text-lg font-bold card-title text-base-content">
                                    <svg class="w-5 h-5 mr-2 text-primary" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                    {{ __('dashboard.invoices_to_pay') }}
                                </h3>
                                <div class="my-3 divider"></div>
                                @if ($pendingInvoices->count() > 0)
                                    <div class="space-y-5">
                                        @foreach ($pendingInvoices as $invoice)
                                            <div
                                                class="flex items-center justify-between p-3 transition-colors duration-200 rounded-lg bg-base-100 hover:bg-base-300">
                                                <div>
                                                    <p class="font-medium">{{ $invoice->invoice_number }}</p>
                                                    <p class="text-sm text-base-content/70">{{ __('invoices.from') }}:
                                                        {{ $invoice->creator->name }}</p>
                                                    <p class="text-sm">
                                                        @if ($invoice->status === 'sent')
                                                            <span
                                                                class="badge badge-info">{{ __('invoices.sent') }}</span>
                                                        @elseif($invoice->status === 'partial')
                                                            <span
                                                                class="badge badge-warning">{{ __('invoices.partial') }}</span>
                                                        @elseif($invoice->status === 'overdue')
                                                            <span
                                                                class="badge badge-error">{{ __('invoices.overdue') }}</span>
                                                        @endif
                                                        <span class="ml-2">{{ $invoice->formatted_total }}</span>
                                                    </p>
                                                </div>
                                                <a href="{{ route('invoices.pay', $invoice) }}"
                                                    class="btn btn-sm btn-primary">{{ __('invoices.pay_now') }}</a>
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <div class="flex flex-col items-center justify-center py-8 text-center">
                                        <svg class="w-12 h-12 mb-3 text-base-content/30" fill="none"
                                            stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                        <p class="text-base-content/70">{{ __('dashboard.no_pending_invoices') }}</p>
                                    </div>
                                @endif

                                <div class="justify-end mt-4 card-actions">
                                    <a href="{{ route('invoices.received') }}"
                                        class="btn btn-sm btn-ghost">{{ __('app.view_all') }}</a>
                                </div>
                            </div>
                        </div>

                        <!-- Sent Invoices Widget -->
                        <div class="transition-shadow duration-300 shadow-xl card bg-base-200 hover:shadow-2xl">
                            <div class="card-body">
                                <h3 class="text-lg font-bold card-title text-base-content">
                                    <svg class="w-5 h-5 mr-2 text-primary" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                    </svg>
                                    {{ __('dashboard.invoices_sent') }}
                                </h3>
                                <div class="my-3 divider"></div>


                                @if ($sentInvoices->count() > 0)
                                    <div class="space-y-5">
                                        @foreach ($sentInvoices as $invoice)
                                            <div
                                                class="flex items-center justify-between p-3 transition-colors duration-200 rounded-lg bg-base-100 hover:bg-base-300">
                                                <div>
                                                    <p class="font-medium">{{ $invoice->invoice_number }}</p>
                                                    <p class="text-sm text-base-content/70">{{ __('invoices.to') }}:
                                                        {{ $invoice->recipient->name }}</p>
                                                    <p class="text-sm">
                                                        @if ($invoice->status === 'draft')
                                                            <span
                                                                class="badge badge-ghost">{{ __('invoices.draft') }}</span>
                                                        @elseif($invoice->status === 'sent')
                                                            <span
                                                                class="badge badge-info">{{ __('invoices.sent') }}</span>
                                                        @elseif($invoice->status === 'partial')
                                                            <span
                                                                class="badge badge-warning">{{ __('invoices.partial') }}</span>
                                                        @elseif($invoice->status === 'paid')
                                                            <span
                                                                class="badge badge-success">{{ __('invoices.paid') }}</span>
                                                        @elseif($invoice->status === 'overdue')
                                                            <span
                                                                class="badge badge-error">{{ __('invoices.overdue') }}</span>
                                                        @elseif($invoice->status === 'cancelled')
                                                            <span
                                                                class="badge badge-error">{{ __('invoices.cancelled') }}</span>
                                                        @endif
                                                        <span class="ml-2">{{ $invoice->formatted_total }}</span>
                                                    </p>
                                                </div>
                                                <a href="{{ route('invoices.show', $invoice) }}"
                                                    class="btn btn-sm btn-ghost">{{ __('app.view') }}</a>
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <div class="flex flex-col items-center justify-center py-8 text-center">
                                        <svg class="w-12 h-12 mb-3 text-base-content/30" fill="none"
                                            stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                        </svg>
                                        <p class="text-base-content/70">{{ __('dashboard.no_sent_invoices') }}</p>
                                    </div>
                                @endif

                                <div class="justify-end mt-4 card-actions">
                                    <a href="{{ route('invoices.create') }}"
                                        class="btn btn-sm btn-primary">{{ __('invoices.create_invoice') }}</a>
                                    <a href="{{ route('invoices.index') }}"
                                        class="btn btn-sm btn-ghost">{{ __('app.view_all') }}</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <form method="POST" action="{{ route('logout') }}" class="justify-center card-actions">
        @csrf
        <button type="submit" class="btn btn-outline">{{ __('Log Out') }}</button>
    </form>
</x-app-layout>
