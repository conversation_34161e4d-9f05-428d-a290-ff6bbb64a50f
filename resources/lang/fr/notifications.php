<?php

return [
    'title' => 'Notifications',
    'mark_all_read' => 'Tout marquer comme lu',
    'mark_read' => 'Marquer comme lu',
    'no_notifications' => 'Aucune nouvelle notification',
    'view_case' => 'Voir le dossier',
    'new_notification' => 'Nouvelle notification reçue',
    'invite_received' => 'Vous avez été invité à collaborer sur le dossier ":case" en tant que :role',
    'access_revoked' => 'Votre accès au dossier ":case" a été révoqué',
    'role_changed' => 'Votre rôle pour le dossier ":case" a été changé en :role',
    'contacts_processed' => ':count contacts traités avec succès',
    'contacts_processing_failed' => 'Échec du traitement des contacts : :message',
    'research_items_generated' => ':count éléments de recherche générés',
    'research_initiated' => 'Recherche lancée pour ":title"',
    'research_retry_initiated' => 'Nouvelle tentative de recherche lancée pour ":title"',
    'research_report_removed' => 'Rapport de recherche supprimé de la base de connaissances IA',
    'research_report_removal_failed' => 'Échec de la suppression du rapport de recherche de la base de connaissances IA',
    'research_already_in_progress' => 'Une recherche est déjà en cours pour ":title"',
    'research_only_failed_retry' => 'Seules les recherches échouées peuvent être relancées',
    'no_research_report' => 'Aucun rapport de recherche disponible',
    'no_markdown_content' => 'Aucun contenu markdown disponible',
    'lawyer_review_thanks' => 'Merci de votre intérêt. Un avocat pourra vous contacter après avoir terminé l\'ensemble du processus d\'entretien.',
    'party_added' => ':name ajouté aux parties du dossier',
    'invitation_sent' => 'Invitation envoyée avec succès.',
    'permissions_updated' => 'Autorisations mises à jour avec succès.',
    'permissions_update_failed' => 'Échec de la mise à jour des autorisations.',
    'error' => 'Erreur : :message'
];
