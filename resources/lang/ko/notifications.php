<?php

return [
    'title' => '알림',
    'mark_all_read' => '모두 읽음으로 표시',
    'mark_read' => '읽음으로 표시',
    'no_notifications' => '새 알림이 없습니다',
    'view_case' => '사례 보기',
    'new_notification' => '새 알림이 도착했습니다',
    'invite_received' => '사례 ":case"에 대해 :role으로 협업 초대를 받았습니다',
    'access_revoked' => '사례 ":case"에 대한 접근 권한이 철회되었습니다',
    'role_changed' => '사례 ":case"에 대한 역할이 :role으로 변경되었습니다',
    'contacts_processed' => ':count 연락처가 성공적으로 처리되었습니다',
    'contacts_processing_failed' => '연락처 처리 실패: :message',
    'research_items_generated' => ':count 개의 연구 항목이 생성되었습니다',
    'research_initiated' => '" :title "에 대한 연구가 시작되었습니다',
    'research_retry_initiated' => '" :title "에 대한 연구 재시도가 시작되었습니다',
    'research_report_removed' => '연구 보고서가 AI 지식 베이스에서 삭제되었습니다',
    'research_report_removal_failed' => 'AI 지식 베이스에서 연구 보고서 삭제 실패',
    'research_already_in_progress' => '" :title "에 대한 연구가 이미 진행 중입니다',
    'research_only_failed_retry' => '실패한 연구만 재시도할 수 있습니다',
    'no_research_report' => '연구 보고서가 없습니다',
    'no_markdown_content' => '마크다운 콘텐츠가 없습니다',
    'lawyer_review_thanks' => '관심 가져주셔서 감사합니다. 전체 인터뷰 과정을 완료하시면 변호사가 연락드릴 수 있습니다.',
    'party_added' => ':name이 사건 당사자에 추가되었습니다',
    'invitation_sent' => '초대장이 성공적으로 발송되었습니다.',
    'permissions_updated' => '권한이 성공적으로 업데이트되었습니다.',
    'permissions_update_failed' => '권한 업데이트에 실패했습니다.',
    'error' => '오류: :message'
];
