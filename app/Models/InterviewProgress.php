<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InterviewProgress extends Model
{
    use HasFactory;

    protected $fillable = [
        'case_file_id',
        'current_step',
        'completed_steps',
        'step_data',
        'last_activity',
    ];

    protected $casts = [
        'completed_steps' => 'array',
        'step_data' => 'array',
        'last_activity' => 'datetime',
    ];

    public function caseFile()
    {
        return $this->belongsTo(CaseFile::class);
    }
}