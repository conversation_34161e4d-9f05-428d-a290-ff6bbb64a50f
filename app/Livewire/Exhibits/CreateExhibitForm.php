<?php

namespace App\Livewire\Exhibits;

use App\Models\Document;
use App\Models\Exhibit;
use App\Services\ExhibitService;
use Livewire\Component;

class CreateExhibitForm extends Component
{
    public $caseFileId;
    public $draftId;
    public $label;
    public $description;
    public $documentId;
    public $dated;
    
    public $availableDocuments = [];
    
    protected $rules = [
        'label' => 'required|string|max:10',
        'description' => 'required|string|max:255',
        'documentId' => 'nullable|exists:documents,id',
        'dated' => 'nullable|date'
    ];
    
    public function mount($caseFileId, $draftId = null)
    {
        $this->caseFileId = $caseFileId;
        $this->draftId = $draftId;
        
        // Get next available exhibit label
        $exhibitService = app(ExhibitService::class);
        $this->label = $exhibitService->getNextExhibitLabel($this->caseFileId);
        
        // Load available documents
        $this->loadAvailableDocuments();
    }
    
    protected function loadAvailableDocuments()
    {
        $this->availableDocuments = Document::where('case_file_id', $this->caseFileId)
            ->whereDoesntHave('exhibit')
            ->get();
    }
    
    public function save()
    {
        $this->validate();
        
        // Get next sort order
        $maxSortOrder = Exhibit::where('case_file_id', $this->caseFileId)
            ->max('sort_order') ?? 0;
            
        // Create the exhibit
        $exhibit = Exhibit::create([
            'case_file_id' => $this->caseFileId,
            'draft_id' => $this->draftId,
            'document_id' => $this->documentId,
            'label' => $this->label,
            'description' => $this->description,
            'sort_order' => $maxSortOrder + 1,
            'dated' => $this->dated
        ]);
        
        // Reset form
        $this->reset(['label', 'description', 'documentId', 'dated']);
        
        // Get next available exhibit label
        $exhibitService = app(ExhibitService::class);
        $this->label = $exhibitService->getNextExhibitLabel($this->caseFileId);
        
        $this->loadAvailableDocuments();
        
        // Emit event to refresh exhibits
        $this->dispatch('exhibitAdded');
        $this->dispatch('closeModal');
    }
    
    public function render()
    {
        return view('livewire.exhibits.create-exhibit-form');
    }
}
