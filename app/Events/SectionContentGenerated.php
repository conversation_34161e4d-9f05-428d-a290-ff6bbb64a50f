<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SectionContentGenerated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $draftId;
    public $response;
    public $chatMessage;
    public $sectionId;
    public $userId;

    /**
     * Create a new event instance.
     *
     * @param int $draftId
     * @param array $response
     * @param array $chatMessage
     * @param string $sectionId
     * @param int $userId
     */
    public function __construct(int $draftId, array $response, array $chatMessage, string $sectionId, int $userId)
    {
        $this->draftId = $draftId;
        $this->response = $response;
        $this->chatMessage = $chatMessage;
        $this->sectionId = $sectionId;
        $this->userId = $userId;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new Channel('draft.' . $this->draftId),
        ];
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs(): string
    {
        return 'section.content.generated';
    }
}
