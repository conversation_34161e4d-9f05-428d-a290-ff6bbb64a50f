<?php

namespace App\Http\Controllers;

use App\Models\ConnectAccount;
use App\Models\Invoice;
use App\Models\InvoicePayment;
use App\Models\User;
use App\Services\ConnectService;
use App\Services\CreditService;
use App\Services\InvoiceService;
use Illuminate\Support\Facades\Log;
use Laravel\Cashier\Http\Controllers\WebhookController as CashierController;

class StripeWebhookController extends CashierController
{
    /**
     * Handle invoice payment succeeded.
     *
     * @param array $payload
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function handleInvoicePaymentSucceeded(array $payload)
    {
        $subscription = $this->getSubscriptionByStripeId(
            $payload['data']['object']['subscription'] ?? null
        );

        if ($subscription) {
            $user = $subscription->user;
            $creditService = app(CreditService::class);

            // Determine credits based on plan
            $planCredits = $this->getPlanCredits($subscription->stripe_price);

            if ($planCredits > 0) {
                $creditService->addCredits(
                    $user,
                    $planCredits,
                    'Monthly subscription credits',
                    [
                        'subscription_id' => $subscription->id,
                        'invoice_id' => $payload['data']['object']['id'] ?? null,
                    ]
                );
            }
        }

        return $this->successMethod();
    }

    /**
     * Handle customer subscription trial will end.
     *
     * @param array $payload
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function handleCustomerSubscriptionTrialWillEnd(array $payload)
    {
        $subscription = $this->getSubscriptionByStripeId(
            $payload['data']['object']['id'] ?? null
        );

        if ($subscription) {
            $user = $subscription->user;

            // Check if this is a Pro Se Basic commitment subscription
            $stripeSubscription = $payload['data']['object'];
            $metadata = $stripeSubscription['metadata'] ?? [];

            if (isset($metadata['commitment_type']) && $metadata['commitment_type'] === 'pro_se_basic_3_month') {
                // Send notification about upcoming billing
                Log::info('Pro Se Basic commitment trial ending soon', [
                    'user_id' => $user->id,
                    'subscription_id' => $subscription->stripe_id,
                    'trial_end' => $stripeSubscription['trial_end'],
                ]);

                // You could send an email notification here
                // Mail::to($user)->send(new TrialEndingNotification($user, $subscription));
            }
        }

        return $this->successMethod();
    }

    /**
     * Get the number of credits for a subscription plan
     *
     * @param string $stripePriceId
     * @return int
     */
    protected function getPlanCredits(string $stripePriceId): int
    {
        $plans = config('stripe.plans', []);
        return $plans[$stripePriceId]['credits'] ?? 0;
    }

    /**
     * Handle account updated event.
     *
     * @param array $payload
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function handleAccountUpdated(array $payload)
    {
        $accountId = $payload['data']['object']['id'] ?? null;

        if ($accountId) {
            $connectAccount = ConnectAccount::where('stripe_account_id', $accountId)->first();

            if ($connectAccount) {
                $connectService = app(ConnectService::class);
                $connectService->syncConnectAccount($connectAccount);

                Log::info('Connect account updated via webhook', [
                    'account_id' => $accountId,
                    'user_id' => $connectAccount->user_id,
                ]);
            }
        }

        return $this->successMethod();
    }

    /**
     * Handle payment intent succeeded event.
     *
     * @param array $payload
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function handlePaymentIntentSucceeded(array $payload)
    {
        $paymentIntent = $payload['data']['object'] ?? null;
        $invoiceId = $paymentIntent['metadata']['invoice_id'] ?? null;

        if ($invoiceId) {
            $invoice = Invoice::find($invoiceId);

            if ($invoice) {
                // Check if we already have a payment record for this payment intent
                $existingPayment = InvoicePayment::where('stripe_payment_intent_id', $paymentIntent['id'])->first();

                if (!$existingPayment) {
                    // This is a new payment, create a payment record
                    $payerId = $paymentIntent['metadata']['payer_id'] ?? null;
                    $payer = $payerId ? User::find($payerId) : null;

                    if ($payer) {
                        $invoiceService = app(InvoiceService::class);

                        try {
                            // Create a payment record
                            $payment = new InvoicePayment([
                                'invoice_id' => $invoice->id,
                                'amount_cents' => $paymentIntent['amount'],
                                'payment_method' => 'credit_card',
                                'stripe_payment_intent_id' => $paymentIntent['id'],
                                'status' => 'completed',
                                'paid_by_id' => $payer->id,
                                'metadata' => [
                                    'payment_intent_id' => $paymentIntent['id'],
                                    'application_fee_amount' => $paymentIntent['application_fee_amount'] ?? 0,
                                ],
                            ]);

                            $payment->save();

                            // Update the invoice status
                            $totalPaid = $invoice->getTotalPaidAttribute();

                            if ($totalPaid >= $invoice->total_cents) {
                                $invoiceService->updateInvoiceStatus($invoice, 'paid');
                            } else {
                                $invoiceService->updateInvoiceStatus($invoice, 'partial');
                            }

                            Log::info('Payment intent succeeded via webhook', [
                                'payment_intent_id' => $paymentIntent['id'],
                                'invoice_id' => $invoice->id,
                                'amount' => $paymentIntent['amount'],
                            ]);
                        } catch (\Exception $e) {
                            Log::error('Failed to process payment intent webhook', [
                                'payment_intent_id' => $paymentIntent['id'],
                                'invoice_id' => $invoice->id,
                                'error' => $e->getMessage(),
                            ]);
                        }
                    }
                }
            }
        }

        return $this->successMethod();
    }
}
