<?php

namespace App\Services\LegalResearch\Services;

use App\Services\LegalResearch\Contracts\ResearchServiceInterface;
use App\Services\OpenAI\OpenAIService;

class GptResearcherService implements ResearchServiceInterface
{
    public function __construct(
        private readonly OpenAIService $openAI
    ) {}

    public function research(string $query, array $parameters, int $caseFileId): array
    {
        // Implementation coming in next phase
        return [];
    }

    public function canHandle(string $query, array $parameters): bool
    {
        return true; // This is our fallback service that can handle any query
    }

    public function getExpectedResponseTime(): int
    {
        return 300; // 5 minutes
    }
}