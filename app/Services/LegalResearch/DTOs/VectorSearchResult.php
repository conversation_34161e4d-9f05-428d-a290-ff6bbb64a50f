<?php

namespace App\Services\LegalResearch\DTOs;

class VectorSearchResult
{
    public function __construct(
        public readonly array $results,
        public readonly float $avgSimilarity,
        public readonly ?object $topMatch,
        public readonly int $totalResults = 0,
        public readonly ?string $assistantId = null,
        public readonly ?string $vectorStoreId = null
    ) {}

    public function hasResults(): bool
    {
        return !empty($this->results);
    }

    public function getTopSimilarity(): float
    {
        return $this->topMatch?->similarity ?? 0.0;
    }

    public function isHighConfidence(): bool
    {
        return $this->avgSimilarity > 0.8;
    }

    public function isMediumConfidence(): bool
    {
        return $this->avgSimilarity > 0.5 && $this->avgSimilarity <= 0.8;
    }

    public function isLowConfidence(): bool
    {
        return $this->avgSimilarity <= 0.5;
    }

    public function getRelevantCitations(): array
    {
        return array_filter($this->results, fn($result) =>
            isset($result->metadata['type']) &&
            $result->metadata['type'] === 'citation'
        );
    }

    public function getRelevantDocuments(): array
    {
        return array_filter($this->results, fn($result) =>
            isset($result->metadata['type']) &&
            $result->metadata['type'] === 'document'
        );
    }

    public function hasAssistantContext(): bool
    {
        return !empty($this->assistantId);
    }
}
