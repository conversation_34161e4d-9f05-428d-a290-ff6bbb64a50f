<?php

namespace App\Services;

use App\Models\Draft;
use App\Models\CaseFile;
use App\Models\Exhibit;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\IOFactory;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Services\HtmlToDocumentConverter;
use App\Services\DocumentConversionService;
use setasign\Fpdi\Tcpdf\Fpdi;

class DocumentGenerationService
{
    /**
     * Document conversion service
     */
    protected $conversionService;

    protected $lineHeight = 2.0;

    /**
     * Default document settings
     */
    protected $defaultSettings = [
        'fontName' => 'Times New Roman',
        'fontSize' => 14,
        'lineHeight' => 2.0,
        'marginTop' => 1440, // 1 inch in twips
        'marginBottom' => 1440,
        'marginLeft' => 1440,
        'marginRight' => 1440
    ];

    /**
     * Generate a document from a draft
     *
     * @param Draft $draft The draft to generate a document from
     * @param array $options Additional options for document generation
     * @param array $documentIds Optional array of document IDs to include as exhibits
     * @return string The path to the generated document
     */
    public function generateDocument(Draft $draft, array $options = [], array $documentIds = [])
    {
        // Create a new PhpWord instance
        $phpWord = new PhpWord();

        // Set line height based on document type
        if (!in_array($draft->draft_type, ['complaint', 'pleading', 'answer', 'motion', 'proposed_order', 'affidavit'])) {
            $this->lineHeight = 1.0;
        }

        // Apply document settings
        $this->applyDocumentSettings($phpWord, $draft, $options);

        // Create the main section
        $section = $phpWord->addSection([
            'marginTop' => $options['marginTop'] ?? $this->defaultSettings['marginTop'],
            'marginBottom' => $options['marginBottom'] ?? $this->defaultSettings['marginBottom'],
            'marginLeft' => $options['marginLeft'] ?? $this->defaultSettings['marginLeft'],
            'marginRight' => $options['marginRight'] ?? $this->defaultSettings['marginRight'],
        ]);

        // Get the sections structure from the draft
        $sections = $draft->sections_structure ?? [];

        // Process each section based on its type
        foreach ($sections as $draftSection) {
            if (empty($draftSection['content'])) {
                continue;
            }

            $this->processSection($section, $draftSection, $draft);
        }

        // Generate a filename
        $filename = $this->generateFilename($draft);

        // Make sure the documents directory exists
        $directory = storage_path('app/public/documents');
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }

        // Save the document
        $path = storage_path("app/public/documents/{$filename}");
        $objWriter = IOFactory::createWriter($phpWord, 'Word2007');
        $objWriter->save($path);
        // Save the MAIN document to S3
        $s3Path = Storage::disk('s3')->putFileAs('documents', $path, $filename);

        // If we have documents to include as exhibits, process them
        if (!empty($documentIds)) {
            $this->processDocumentWithExhibits($path, $draft, $documentIds);
        }

        // Return the relative path for storage
        return $s3Path;
    }

    /**
     * Apply document settings to the PhpWord instance
     */
    protected function applyDocumentSettings(PhpWord $phpWord, Draft $draft, array $options = [])
    {
        // Get document metadata if available
        $metadata = $draft->metadata ?? [];

        // Determine if we should use formal formatting (double spacing)
        $useFormalFormatting = $this->shouldUseFormalFormatting($draft);

        // Set default font
        $fontName = $options['fontName'] ?? $metadata['fontName'] ?? $this->defaultSettings['fontName'];
        $fontSize = $options['fontSize'] ?? $metadata['fontSize'] ?? $this->defaultSettings['fontSize'];



        $phpWord->setDefaultFontName($fontName);
        $phpWord->setDefaultFontSize($fontSize);

        // Add document properties
        $properties = $phpWord->getDocInfo();
        $properties->setCreator($draft->creator ? $draft->creator->name : 'Document Generator');
        $properties->setTitle($draft->description ?? 'Legal Document');
        $properties->setSubject($draft->draft_type ?? 'Legal Document');
        $properties->setDescription($draft->description ?? '');
        if(!in_array($draft->draft_type, ['complaint', 'motion', 'pleading', 'answer', 'proposed_order', 'affidavit'])){
            $lineHeight = 1.0;
        } else {
            $lineHeight = 2.0;
        }

        // Add custom styles with appropriate line height
        $this->addCustomStyles($phpWord, $lineHeight);
    }

    /**
     * Add custom styles to the document
     *
     * @param PhpWord $phpWord The PhpWord instance
     * @param float $lineHeight The line height to use for paragraphs (default: 2.0)
     */
    protected function addCustomStyles(PhpWord $phpWord, float $lineHeight = 2.0)
    {
        // Heading styles
        $phpWord->addTitleStyle(1, ['bold' => true, 'size' => 16, 'allCaps' => true], ['alignment' => 'center', 'spaceBefore' => 240, 'spaceAfter' => 120]);
        $phpWord->addTitleStyle(2, ['bold' => true, 'size' => 14, 'allCaps' => true], ['alignment' => 'center', 'spaceBefore' => 240, 'spaceAfter' => 120]);
        $phpWord->addTitleStyle(3, ['bold' => true, 'size' => 14, 'underline' => 'single'], ['alignment' => 'left', 'spaceBefore' => 240, 'spaceAfter' => 120]);

        // Paragraph styles
        $phpWord->addParagraphStyle('normal', [
            'alignment' => 'both',
            'lineHeight' => $lineHeight,
            'spaceBefore' => 0,
            'spaceAfter' => 0
        ]);

        $phpWord->addParagraphStyle('indented', [
            'alignment' => 'both',
            'indentation' => ['left' => 720],
            'lineHeight' => $lineHeight,
            'spaceBefore' => 0,
            'spaceAfter' => 0
        ]);

        $phpWord->addParagraphStyle('numbered', [
            'alignment' => 'both',
            'indentation' => ['left' => 720, 'hanging' => 360],
            'lineHeight' => $lineHeight,
            'spaceBefore' => 0,
            'spaceAfter' => 0
        ]);

        $phpWord->addParagraphStyle('centered', [
            'alignment' => 'center',
            'lineHeight' => $lineHeight,
            'spaceBefore' => 240,
            'spaceAfter' => 240
        ]);

        $phpWord->addParagraphStyle('right-aligned', [
            'alignment' => 'right',
            'lineHeight' => $lineHeight,
            'spaceBefore' => 0,
            'spaceAfter' => 0
        ]);
    }

    /**
     * Determine if a document should use formal headers and double spacing
     *
     * @param Draft $draft The draft to check
     * @return bool True if the document should use formal headers and double spacing
     */
    protected function shouldUseFormalFormatting(Draft $draft)
    {
        // Formal documents include complaints, motions, pleadings, answers, proposed orders, and affidavits
        $formalDocumentTypes = ['complaint', 'motion', 'pleading', 'answer', 'proposed_order', 'affidavit'];

        return in_array(strtolower($draft->draft_type), $formalDocumentTypes);
    }

    /**
     * Check if the document is a letter
     *
     * @param Draft $draft The draft to check
     * @return bool True if the document is a letter
     */
    protected function isLetterDocument(Draft $draft)
    {
        return strtolower($draft->draft_type) === 'letter';
    }

    /**
     * Process a section based on its type
     */
    protected function processSection($section, $draftSection, Draft $draft)
    {
        $sectionType = $draftSection['id'] ?? '';
        $content = $draftSection['content'] ?? '';
        $useFormalFormatting = $this->shouldUseFormalFormatting($draft);

        // We don't need to check for EditorJS format here anymore
        // The HtmlToDocumentConverter will handle it automatically

        // Create HTML converter instance
        $htmlConverter = new HtmlToDocumentConverter();

        // Process based on section type
        switch ($sectionType) {
            case 'caption':
                // Convert HTML to structured data
                $convertedData = $htmlConverter->convert($content, 'caption');
                $this->processCaptionSection($section, $content, $draft, $convertedData);
                break;

            case 'title':
                $convertedData = $htmlConverter->convert($content, 'title');
                $this->processTitleSection($section, $content, $draft, $convertedData);
                break;

            case 'introduction':
                $convertedData = $htmlConverter->convert($content, 'introduction');
                $this->processIntroductionSection($section, $content, $draft, $convertedData);
                break;

            case 'jurisdiction_venue':
                $convertedData = $htmlConverter->convert($content);
                // Only use formal header for formal document types
                $header = $useFormalFormatting ? 'JURISDICTION AND VENUE' : null;
                $this->processTextSection($section, $content, $header, $convertedData);
                break;

            case 'parties':
                $convertedData = $htmlConverter->convert($content);
                // Only use formal header for formal document types
                $header = $useFormalFormatting ? 'PARTIES' : null;
                $this->processTextSection($section, $content, $header, $convertedData);
                break;

            case 'factual_allegations':
                $convertedData = $htmlConverter->convert($content);
                // Only use formal header for formal document types
                $header = $useFormalFormatting ? 'FACTUAL ALLEGATIONS' : null;
                $this->processTextSection($section, $content, $header, $convertedData);
                break;

            case 'causes_of_action':
                $convertedData = $htmlConverter->convert($content, 'causes_of_action');
                $this->processCausesOfActionSection($section, $content, $draft, $convertedData);
                break;

            case 'prayer_for_relief':
                $convertedData = $htmlConverter->convert($content, 'prayer_for_relief');
                $this->processPrayerForReliefSection($section, $content, $draft, $convertedData);
                break;

            case 'signature':
            case 'signature_block':
                $convertedData = $htmlConverter->convert($content, 'signature');
                $this->processSignatureSection($section, $content, $draft, $convertedData);
                break;

            case 'header':
                $convertedData = $htmlConverter->convert($content, 'header');
                $this->processHeaderSection($section, $content, $draft, $convertedData);
                break;

            case 'body':
                $convertedData = $htmlConverter->convert($content, 'body');
                $this->processBodySection($section, $content, $draft, $convertedData);
                break;

            case 'background':
                $convertedData = $htmlConverter->convert($content);
                // Only use formal header for formal document types
                $header = $useFormalFormatting ? 'BACKGROUND' : null;
                $this->processTextSection($section, $content, $header, $convertedData);
                break;

            case 'legal_standard':
                $convertedData = $htmlConverter->convert($content);
                // Only use formal header for formal document types
                $header = $useFormalFormatting ? 'LEGAL STANDARD' : null;
                $this->processTextSection($section, $content, $header, $convertedData);
                break;

            case 'argument':
                $convertedData = $htmlConverter->convert($content);
                // Only use formal header for formal document types
                $header = $useFormalFormatting ? 'ARGUMENT' : null;
                $this->processTextSection($section, $content, $header, $convertedData);
                break;

            // Proposed Order sections
            case 'preamble':
                $convertedData = $htmlConverter->convert($content);
                $this->processTextSection($section, $content, null, $convertedData);
                break;

            case 'findings':
                $convertedData = $htmlConverter->convert($content);
                $header = $useFormalFormatting ? 'FINDINGS OF FACT' : null;
                $this->processTextSection($section, $content, $header, $convertedData);
                break;

            case 'conclusions':
                $convertedData = $htmlConverter->convert($content);
                $header = $useFormalFormatting ? 'CONCLUSIONS OF LAW' : null;
                $this->processTextSection($section, $content, $header, $convertedData);
                break;

            case 'order_terms':
                $convertedData = $htmlConverter->convert($content);
                $header = $useFormalFormatting ? 'ORDER' : null;
                $this->processTextSection($section, $content, $header, $convertedData);
                break;

            // Certificate of Service sections
            case 'document_served':
            case 'service_details':
            case 'recipients':
            case 'certification':
                $convertedData = $htmlConverter->convert($content);
                $this->processTextSection($section, $content, null, $convertedData);
                break;

            // Affidavit sections
            case 'affiant_intro':
            case 'oath_statement':
                $convertedData = $htmlConverter->convert($content);
                $this->processTextSection($section, $content, null, $convertedData);
                break;

            case 'factual_statements':
                $convertedData = $htmlConverter->convert($content);
                $header = $useFormalFormatting ? 'FACTUAL STATEMENTS' : null;
                $this->processTextSection($section, $content, $header, $convertedData);
                break;

            case 'notary_block':
                $convertedData = $htmlConverter->convert($content);
                $this->processNotaryBlockSection($section, $content, $draft, $convertedData);
                break;

            case 'certificate_of_service':
                $convertedData = $htmlConverter->convert($content);
                $header = $useFormalFormatting ? 'CERTIFICATE OF SERVICE' : null;
                $this->processTextSection($section, $content, $header, $convertedData);
                break;

            default:
                // For any other section type, process as generic text
                $convertedData = $htmlConverter->convert($content);
                // For non-formal documents, don't use the section name as a header
                if (!$useFormalFormatting) {
                    $this->processTextSection($section, $content, null, $convertedData);
                } else {
                    $this->processGenericSection($section, $draftSection, $draft, $convertedData);
                }
                break;
        }
    }

    /**
     * Process the caption section
     */
    protected function processCaptionSection($section, $content, Draft $draft, $convertedData = null)
    {
        $caseFile = $draft->caseFile;
        $captionData = null;

        // First check if we have caption_data in the draft
        if (!empty($draft->caption_data)) {
            $captionData = $draft->caption_data;
            \Illuminate\Support\Facades\Log::info('Using caption data from dedicated column', [
                'draft_id' => $draft->id,
                'format' => $captionData['_format'] ?? 'unknown'
            ]);
        }
        // Fallback to content if caption_data is not available
        else if (is_string($content) && $this->isValidJson($content)) {
            $captionData = json_decode($content, true);
            \Illuminate\Support\Facades\Log::info('Using caption data from section content', [
                'draft_id' => $draft->id
            ]);
        }

        // Extract court information from content or case file
        if ($captionData && isset($captionData['courtName'])) {
            // Use data from the caption form
            $courtInfo = [
                'courtName' => $captionData['courtName'],
                'division' => $captionData['division'] ?? ''
            ];
        } else if ($convertedData && isset($convertedData['courtInfo'])) {
            $courtInfo = $convertedData['courtInfo'];
        } else {
            $courtInfo = $this->extractCourtInfo($content, $caseFile);
        }

        // Add court name
        $section->addText(
            'IN THE ' . $courtInfo['courtName'],
            ['bold' => true, 'allCaps' => true, 'size' => 14],
            ['alignment' => 'center']
        );

        // Add division if available
        if (!empty($courtInfo['division'])) {
            $section->addText(
                $courtInfo['division'],
                ['bold' => true, 'size' => 14],
                ['alignment' => 'center']
            );
        }

        // Space before caption table
        $section->addText('', ['size' => 14], ['spacing' => 120]);

        // Create caption table
        $table = $section->addTable([
            'width' => 100 * 50,
            'unit' => 'pct',
            'alignment' => 'center',
            'layout' => 'fixed'
        ]);

        $row = $table->addRow();

        // Left cell (parties)
        $cell1 = $row->addCell(5000, [
            'valign' => 'center',
            'borderRightSize' => 1,
            'borderRightColor' => '000000',
            'spaceAfter' => 0,
            'spaceBefore' => 0,
            'padding' => 120,
        ]);

        // Process parties text
        if ($captionData && isset($captionData['plaintiffs']) && isset($captionData['defendants'])) {
            // Use data from the caption form
            $parties = [];

            // Add plaintiffs
            foreach ($captionData['plaintiffs'] as $plaintiff) {
                if (!empty($plaintiff['name'])) {
                    $parties[] = $plaintiff['name'] . ',';
                    $parties[] = '';
                    $parties[] = $plaintiff['role'] . ','; // Plaintiff or Petitioner
                    $parties[] = '';
                }
            }

            // Add 'v.' separator
            $parties[] = 'v.';
            $parties[] = '';

            // Add defendants
            foreach ($captionData['defendants'] as $defendant) {
                if (!empty($defendant['name'])) {
                    $parties[] = $defendant['name'] . ',';
                    $parties[] = '';
                    $parties[] = $defendant['role'] . ','; // Defendant or Respondent
                    $parties[] = '';
                }
            }
        } else if ($convertedData && isset($convertedData['parties'])) {
            $parties = $convertedData['parties'];
        } else {
            $parties = $this->extractParties($content, $caseFile);
        }

        foreach ($parties as $party) {
            $cell1->addText($party, ['size' => 14]);
        }

        // Right cell (case info)
        $cell2 = $row->addCell(5000, [
            'valign' => 'center',
            'spaceAfter' => 0,
            'spaceBefore' => 0,
            'padding' => 240,
            'paddingLeft' => 360
        ]);

        // Add case number and judge information
        if ($captionData && isset($captionData['caseNumber'])) {
            // Use data from the caption form
            $cell2->addText('Civil Action File No.', ['size' => 14]);
            $cell2->addText('', ['size' => 14]);
            $cell2->addText($captionData['caseNumber'], ['underline' => 'single', 'size' => 14]);
            $cell2->addText('', ['size' => 14]);

            if (!empty($captionData['judgeName'])) {
                $cell2->addText('Assigned to: ' . $captionData['judgeName'], ['underline' => 'single', 'size' => 14]);
            }
        } else {
            // Default case information
            $cell2->addText('Civil Action File No.', ['size' => 14]);
            $cell2->addText('', ['size' => 14]);
            $cell2->addText($caseFile->case_number ?? '___________________', ['underline' => 'single', 'size' => 14]);
            $cell2->addText('', ['size' => 14]);
            $cell2->addText('Assigned to: ___________________', ['underline' => 'single', 'size' => 14]);
        }

        // Extra space after caption (table)
        $section->addText('', ['size' => 14], ['spacing' => 480]);

        // Add document title if available
        if ($captionData && isset($captionData['documentTitle']) && !empty($captionData['documentTitle'])) {
            $section->addText(
                $captionData['documentTitle'],
                ['bold' => true, 'underline' => 'single', 'size' => 14],
                ['alignment' => 'center']
            );
        }

        // Extra space after caption
        $section->addText('', ['size' => 14], ['spacing' => 480]);
    }

    /**
     * Process the title section
     */
    protected function processTitleSection($section, $content, Draft $draft, $convertedData = null)
    {
        // Check if content is JSON and we don't have converted data yet
        if (!$convertedData && is_string($content) && $this->isValidJson($content)) {
            try {
                $jsonData = json_decode($content, true);

                // Check if it's a Quill Delta format
                if (isset($jsonData['ops']) && is_array($jsonData['ops'])) {
                    // Create HTML converter instance
                    $htmlConverter = new HtmlToDocumentConverter();
                    $convertedData = $htmlConverter->convert($content, 'title');
                }
            } catch (\Exception $e) {
                // If JSON parsing fails, continue with existing data
            }
        }

        if ($convertedData && isset($convertedData['paragraphs']) && !empty($convertedData['paragraphs'])) {
            // Use the first paragraph as the title
            $titleParagraph = $convertedData['paragraphs'][0];
            $title = $titleParagraph['text'];

            // Get any custom formatting
            $format = $titleParagraph['format'] ?? [];

            // Set default title formatting
            $fontStyle = ['bold' => true, 'underline' => 'single', 'size' => 14];
            $paraStyle = ['alignment' => 'center'];

            // Apply any custom formatting
            if (isset($format['size'])) {
                $fontStyle['size'] = $format['size'];
            }

            if (isset($format['alignment'])) {
                $paraStyle['alignment'] = $format['alignment'];
            }

            // If this is a header, make it bold and centered
            if (isset($format['header'])) {
                $fontStyle['bold'] = true;
                $paraStyle['alignment'] = 'center';

                // Adjust size based on header level
                if ($format['header'] == 1) {
                    $fontStyle['size'] = 16;
                } else if ($format['header'] == 2) {
                    $fontStyle['size'] = 15;
                }
            }

            // Add the title
            $section->addText(
                $title,
                $fontStyle,
                $paraStyle
            );

            // Process additional paragraphs if they exist (for motion introductory text)
            if (count($convertedData['paragraphs']) > 1) {
                // Add space after title
                $section->addText('', ['size' => 14], ['spacing' => 120]);

                // Process remaining paragraphs
                for ($i = 1; $i < count($convertedData['paragraphs']); $i++) {
                    $paragraph = $convertedData['paragraphs'][$i];

                    // Skip empty paragraphs
                    if (empty(trim($paragraph['text']))) {
                        continue;
                    }

                    // Get formatting
                    $format = $paragraph['format'] ?? [];

                    // Set default formatting
                    $fontStyle = ['size' => 14];
                    $paraStyle = ['alignment' => 'both', 'lineHeight' => 2.0];

                    // Apply formatting
                    if (isset($format['bold']) && $format['bold']) {
                        $fontStyle['bold'] = true;
                    }
                    if (isset($format['italic']) && $format['italic']) {
                        $fontStyle['italic'] = true;
                    }
                    if (isset($format['underline'])) {
                        $fontStyle['underline'] = $format['underline'];
                    }
                    if (isset($format['alignment'])) {
                        $paraStyle['alignment'] = $format['alignment'];
                    }
                    if (isset($format['indentation'])) {
                        $paraStyle['indentation'] = $format['indentation'];
                    }

                    // Add the paragraph
                    $section->addText(
                        $paragraph['text'],
                        $fontStyle,
                        $paraStyle
                    );
                }
            }
        } else if (is_string($content) && $this->isValidJson($content)) {
            try {
                $jsonData = json_decode($content, true);

                // If it's Quill Delta format, extract title and content
                if (isset($jsonData['ops']) && is_array($jsonData['ops'])) {
                    $title = '';
                    $foundTitle = false;

                    foreach ($jsonData['ops'] as $op) {
                        if (isset($op['insert'])) {
                            $text = $op['insert'];
                            $attributes = $op['attributes'] ?? [];

                            // Check for header (title)
                            if (!$foundTitle && $text !== "\n" && isset($attributes['header'])) {
                                $title = $text;
                                $foundTitle = true;

                                // Add the title
                                $section->addText(
                                    $title,
                                    ['bold' => true, 'underline' => 'single', 'size' => 16],
                                    ['alignment' => 'center']
                                );

                                // Add space after title
                                $section->addText('', ['size' => 14], ['spacing' => 120]);
                            }
                            // Add other paragraphs after the title
                            else if ($foundTitle && $text !== "\n" && !isset($attributes['header'])) {
                                $section->addText(
                                    $text,
                                    ['size' => 14],
                                    ['alignment' => 'both', 'lineHeight' => 2.0]
                                );
                            }
                        }
                    }

                    // If we found and processed the title, return
                    if ($foundTitle) {
                        return;
                    }
                }
            } catch (\Exception $e) {
                // If JSON parsing fails, continue with text processing
            }

            // If we couldn't parse the JSON or didn't find a title, use the content as is
            $title = trim($content);

            $section->addText(
                $title,
                ['bold' => true, 'underline' => 'single', 'size' => 14],
                ['alignment' => 'center']
            );
        } else {
            // Clean up the title
            $title = trim($content);

            // Add the title
            $section->addText(
                $title,
                ['bold' => true, 'underline' => 'single', 'size' => 14],
                ['alignment' => 'center']
            );
        }

        // Add space after title
        $section->addText('', ['size' => 14], ['spacing' => 240]);
    }

    /**
     * Process the introduction section
     */
    protected function processIntroductionSection($section, $content, $convertedData = null)
    {
        // Check if content is JSON and we don't have converted data yet
        if (!$convertedData && is_string($content) && $this->isValidJson($content)) {
            try {
                $jsonData = json_decode($content, true);

                // Check if it's a Quill Delta format
                if (isset($jsonData['ops']) && is_array($jsonData['ops'])) {
                    // Create HTML converter instance
                    $htmlConverter = new HtmlToDocumentConverter();
                    $convertedData = $htmlConverter->convert($content, 'introduction');
                }
            } catch (\Exception $e) {
                // If JSON parsing fails, continue with existing data
            }
        }

        // If we have converted data, check for COMES NOW style
        if ($convertedData && isset($convertedData['comesNowStyle']) && $convertedData['comesNowStyle']) {
            // Process paragraphs with COMES NOW style
            if (isset($convertedData['paragraphs']) && !empty($convertedData['paragraphs'])) {
                foreach ($convertedData['paragraphs'] as $paragraph) {
                    // Skip empty paragraphs
                    if (empty(trim($paragraph['text']))) {
                        continue;
                    }

                    // Get formatting
                    $format = $paragraph['format'] ?? [];

                    // Set default formatting
                    $fontStyle = ['size' => 14];
                    $paraStyle = ['alignment' => 'both', 'lineHeight' => 2.0];

                    // Apply formatting
                    if (isset($format['bold']) && $format['bold']) {
                        $fontStyle['bold'] = true;
                    }
                    if (isset($format['italic']) && $format['italic']) {
                        $fontStyle['italic'] = true;
                    }
                    if (isset($format['underline'])) {
                        $fontStyle['underline'] = $format['underline'];
                    }
                    if (isset($format['alignment'])) {
                        $paraStyle['alignment'] = $format['alignment'];
                    }
                    if (isset($format['indentation'])) {
                        $paraStyle['indentation'] = $format['indentation'];
                    }

                    // Add the paragraph
                    $section->addText(
                        $paragraph['text'],
                        $fontStyle,
                        $paraStyle
                    );
                }
            } else {
                // Fallback to original content
                $section->addText(
                    $content,
                    ['size' => 14],
                    ['alignment' => 'both', 'lineHeight' => 2.0]
                );
            }
        } else if (is_string($content) && $this->isValidJson($content)) {
            try {
                $jsonData = json_decode($content, true);

                // If it's Quill Delta format, extract paragraphs
                if (isset($jsonData['ops']) && is_array($jsonData['ops'])) {
                    $paragraphs = [];
                    $currentText = '';
                    $currentFormat = [];

                    foreach ($jsonData['ops'] as $op) {
                        if (isset($op['insert'])) {
                            $text = $op['insert'];
                            $attributes = $op['attributes'] ?? [];

                            // Check if this is a newline
                            if ($text === "\n") {
                                // End of paragraph
                                if (!empty(trim($currentText))) {
                                    $paragraphs[] = [
                                        'text' => $currentText,
                                        'format' => $currentFormat
                                    ];
                                }

                                // Reset for next paragraph
                                $currentText = '';
                                $currentFormat = [];

                                // Apply paragraph-level formatting
                                foreach ($attributes as $key => $value) {
                                    switch ($key) {
                                        case 'bold':
                                            $currentFormat['bold'] = true;
                                            break;
                                        case 'italic':
                                            $currentFormat['italic'] = true;
                                            break;
                                        case 'underline':
                                            $currentFormat['underline'] = 'single';
                                            break;
                                        case 'align':
                                            $currentFormat['alignment'] = $value;
                                            break;
                                    }
                                }
                            } else {
                                // Add text to current paragraph
                                $currentText .= $text;

                                // Apply character-level formatting
                                foreach ($attributes as $key => $value) {
                                    switch ($key) {
                                        case 'bold':
                                            $currentFormat['bold'] = true;
                                            break;
                                        case 'italic':
                                            $currentFormat['italic'] = true;
                                            break;
                                        case 'underline':
                                            $currentFormat['underline'] = 'single';
                                            break;
                                    }
                                }
                            }
                        }
                    }

                    // Add any remaining text
                    if (!empty(trim($currentText))) {
                        $paragraphs[] = [
                            'text' => $currentText,
                            'format' => $currentFormat
                        ];
                    }

                    // Check if any paragraph contains COMES NOW
                    $comesNowStyle = false;
                    foreach ($paragraphs as $paragraph) {
                        if (stripos($paragraph['text'], 'COMES NOW') !== false || stripos($paragraph['text'], 'COME NOW') !== false) {
                            $comesNowStyle = true;
                            break;
                        }
                    }

                    // Process paragraphs
                    if ($comesNowStyle) {
                        // Process as COMES NOW style
                        foreach ($paragraphs as $paragraph) {
                            // Skip empty paragraphs
                            if (empty(trim($paragraph['text']))) {
                                continue;
                            }

                            // Get formatting
                            $format = $paragraph['format'] ?? [];

                            // Set default formatting
                            $fontStyle = ['size' => 14];
                            $paraStyle = ['alignment' => 'both', 'lineHeight' => 2.0];

                            // Apply formatting
                            if (isset($format['bold']) && $format['bold']) {
                                $fontStyle['bold'] = true;
                            }
                            if (isset($format['italic']) && $format['italic']) {
                                $fontStyle['italic'] = true;
                            }
                            if (isset($format['underline'])) {
                                $fontStyle['underline'] = $format['underline'];
                            }
                            if (isset($format['alignment'])) {
                                $paraStyle['alignment'] = $format['alignment'];
                            }

                            // Add the paragraph
                            $section->addText(
                                $paragraph['text'],
                                $fontStyle,
                                $paraStyle
                            );
                        }
                    } else {
                        // Process as regular text
                        $convertedData = [
                            'paragraphs' => $paragraphs
                        ];
                        $this->processTextSection($section, $content, null, $convertedData);
                    }

                    return;
                }
            } catch (\Exception $e) {
                // If JSON parsing fails, continue with text processing
            }
        } else if (stripos($content, 'COMES NOW') !== false || stripos($content, 'COME NOW') !== false) {
            // Process as a single paragraph
            $section->addText(
                $content,
                ['size' => 14],
                ['alignment' => 'both', 'lineHeight' => 2.0]
            );
        } else {
            // Process as regular text section
            $this->processTextSection($section, $content, null, $convertedData);
        }
    }

    /**
     * Process a generic text section with optional heading
     */
    protected function processTextSection($section, $content, $heading = null, $convertedData = null)
    {
        // Get the PhpWord instance to determine the line height setting
        $phpWord = $section->getPhpWord();

        // Add heading if provided
        if ($heading) {
            $section->addText(
                strtoupper($heading),
                ['bold' => true, 'size' => 14],
                ['alignment' => 'center', 'spaceBefore' => 360, 'spaceAfter' => 240]
            );
        }

        // Create a numbering style for this section if it's a section that typically uses numbering
        $useNumbering = false;
        $numberingStyleName = null;

        if ($heading && in_array(strtolower($heading), ['factual allegations', 'facts', 'jurisdiction and venue', 'parties'])) {
            $useNumbering = true;
            $numberingStyleName = 'numbering' . str_replace(' ', '', $heading);

            // Create the numbering style
            $phpWord = $section->getPhpWord();
            $phpWord->addNumberingStyle(
                $numberingStyleName,
                [
                    'type' => 'multilevel',
                    'levels' => [
                        [
                            'format' => 'decimal',
                            'text' => '%1.',
                            'left' => 360,
                            'hanging' => 360,
                            'tabPos' => 360
                        ],
                        [
                            'format' => 'lowerLetter',
                            'text' => '%2)',
                            'left' => 720,
                            'hanging' => 360,
                            'tabPos' => 720
                        ]
                    ]
                ]
            );
        }

        // If we have converted data, use it
        if ($convertedData && isset($convertedData['paragraphs'])) {
            // If we're using automatic numbering for this section
            if ($useNumbering && $numberingStyleName) {
                foreach ($convertedData['paragraphs'] as $paragraph) {
                    // Skip empty paragraphs
                    if (empty(trim($paragraph['text']))) {
                        continue;
                    }

                    // Get formatting
                    $format = $paragraph['format'] ?? [];

                    // Determine paragraph level based on content
                    $level = $this->determineParagraphLevel($paragraph['text']);

                    // Set default formatting
                    $fontStyle = ['size' => 14];

                    // Apply bold formatting if specified
                    if (isset($format['bold']) && $format['bold']) {
                        $fontStyle['bold'] = true;
                    }

                    // Apply italic formatting if specified
                    if (isset($format['italic']) && $format['italic']) {
                        $fontStyle['italic'] = true;
                    }

                    // Apply underline formatting if specified
                    if (isset($format['underline'])) {
                        $fontStyle['underline'] = $format['underline'];
                    }

                    // Add the paragraph with proper numbering
                    $section->addListItem(
                        $paragraph['text'],
                        $level,
                        $fontStyle,
                        $numberingStyleName,
                        ['alignment' => 'both', 'lineHeight' => $this->lineHeight]
                    );
                }
            } else {
                // Process paragraphs first without automatic numbering
                foreach ($convertedData['paragraphs'] as $paragraph) {
                    // Skip empty paragraphs
                    if (empty(trim($paragraph['text']))) {
                        continue;
                    }

                    // Get formatting
                    $format = $paragraph['format'] ?? [];

                    // Set default formatting
                    $fontStyle = ['size' => 14];
                    $paraStyle = ['alignment' => 'both', 'lineHeight' => $this->lineHeight];

                    // Apply bold formatting if specified
                    if (isset($format['bold']) && $format['bold']) {
                        $fontStyle['bold'] = true;
                    }

                    // Apply italic formatting if specified
                    if (isset($format['italic']) && $format['italic']) {
                        $fontStyle['italic'] = true;
                    }

                    // Apply underline formatting if specified
                    if (isset($format['underline'])) {
                        $fontStyle['underline'] = $format['underline'];
                    }

                    // Apply indentation if specified
                    if (isset($format['indentation'])) {
                        $paraStyle['indentation'] = $format['indentation'];
                    }

                    // Apply alignment if specified
                    if (isset($format['alignment'])) {
                        $paraStyle['alignment'] = $format['alignment'];
                    }

                    // Check if this is a header
                    if (isset($format['header'])) {
                        $headerLevel = $format['header'];
                        $fontStyle['bold'] = true;

                        if ($headerLevel <= 2) {
                            $paraStyle['alignment'] = 'center';
                            $paraStyle['spaceBefore'] = 240;
                            $paraStyle['spaceAfter'] = 120;
                        }
                    }

                    // Add the text
                    $section->addText(
                        $paragraph['text'],
                        $fontStyle,
                        $paraStyle
                    );
                }
            }

            // Process lists if any
            if (isset($convertedData['lists']) && !empty($convertedData['lists'])) {
                foreach ($convertedData['lists'] as $list) {
                    $listItems = $list['items'] ?? [];
                    $isOrdered = $list['ordered'] ?? false;
                    $level = $list['level'] ?? 0;
                    $format = $list['format'] ?? [];

                    // Set default indentation based on level
                    $indentation = $format['indentation'] ?? ['left' => 720 + ($level * 360), 'hanging' => 360];

                    // Set alignment
                    $alignment = $format['alignment'] ?? 'both';

                    // Add list items
                    foreach ($listItems as $index => $item) {
                        // Format the bullet/number
                        $bullet = $isOrdered ? ($index + 1) . '.' : '•';

                        // Add the list item
                        $section->addText(
                            $bullet . ' ' . $item,
                            ['size' => 14],
                            ['alignment' => $alignment, 'indentation' => $indentation, 'lineHeight' => $this->lineHeight]
                        );
                    }

                    // Add a small space after the list
                    if (count($listItems) > 0) {
                        $section->addText('', ['size' => 14], ['spacing' => 120]);
                    }
                }
            }
        } else {
            // Fallback to old method if no converted data
            // Check if content is JSON
            if (is_string($content) && $this->isValidJson($content)) {
                try {
                    $jsonData = json_decode($content, true);

                    // Check if it's a Quill Delta format
                    if (isset($jsonData['ops']) && is_array($jsonData['ops'])) {
                        // Create HTML converter instance
                        $htmlConverter = new HtmlToDocumentConverter();
                        $convertedData = $htmlConverter->convert($content);

                        // Recursively call this method with the converted data
                        $this->processTextSection($section, $content, $heading, $convertedData);
                        return;
                    }
                } catch (\Exception $e) {
                    // If JSON parsing fails, continue with text processing
                }
            }

            // Split content into paragraphs
            $paragraphs = preg_split('/\r\n|\r|\n/', $content);

            // Track if we're in a list
            $inList = false;
            $listItems = [];
            $isOrdered = false;

            foreach ($paragraphs as $paragraph) {
                // Skip empty paragraphs
                if (trim($paragraph) === '') {
                    // If we were in a list and hit an empty line, process the list
                    if ($inList && !empty($listItems)) {
                        $this->addListToSection($section, $listItems, $isOrdered);
                        $listItems = [];
                        $inList = false;
                    }
                    continue;
                }

                // Check if this is a numbered paragraph/list item
                if (preg_match('/^\s*(\d+)\.?\s+(.+)$/', $paragraph, $matches)) {
                    $number = (int)$matches[1];
                    $text = $matches[2];

                    // If this is the first item or follows the sequence, treat as list
                    if (!$inList || $number === 1 || $number === count($listItems) + 1) {
                        $inList = true;
                        $isOrdered = true;
                        $listItems[] = $text;
                    } else {
                        // Not part of a sequence, treat as regular paragraph
                        if ($inList && !empty($listItems)) {
                            $this->addListToSection($section, $listItems, $isOrdered);
                            $listItems = [];
                            $inList = false;
                        }

                        $section->addText(
                            $paragraph,
                            ['size' => 14],
                            ['alignment' => 'both', 'lineHeight' => $this->lineHeight]
                        );
                    }
                }
                // Check if this is a bullet list item
                else if (preg_match('/^\s*[\*\-•]\s+(.+)$/', $paragraph, $matches)) {
                    $text = $matches[1];

                    // If we were in a numbered list, finish it first
                    if ($inList && $isOrdered && !empty($listItems)) {
                        $this->addListToSection($section, $listItems, $isOrdered);
                        $listItems = [];
                    }

                    $inList = true;
                    $isOrdered = false;
                    $listItems[] = $text;
                } else {
                    // Regular paragraph - if we were in a list, finish it first
                    if ($inList && !empty($listItems)) {
                        $this->addListToSection($section, $listItems, $isOrdered);
                        $listItems = [];
                        $inList = false;
                    }

                    $section->addText(
                        $paragraph,
                        ['size' => 14],
                        ['alignment' => 'both', 'lineHeight' => $this->lineHeight]
                    );
                }
            }

            // Process any remaining list items
            if ($inList && !empty($listItems)) {
                $this->addListToSection($section, $listItems, $isOrdered);
            }
        }
    }

    /**
     * Helper method to add a list to a section
     */
    protected function addListToSection($section, $items, $isOrdered = true, ?Draft $draft = null)
    {
        $indentation = ['left' => 720, 'hanging' => 360];

        // Determine line height based on document type
        $lineHeight = ($draft && $this->shouldUseFormalFormatting($draft)) ? 2.0 : 1.0;

        foreach ($items as $index => $item) {
            $bullet = $isOrdered ? ($index + 1) . '.' : '•';

            $section->addText(
                $bullet . ' ' . $item,
                ['size' => 14],
                ['alignment' => 'both', 'indentation' => $indentation, 'lineHeight' => $lineHeight]
            );
        }

        // Add a small space after the list
        if (count($items) > 0) {
            $section->addText('', ['size' => 14], ['spacing' => 120]);
        }
    }

    /**
     * Determine the paragraph level based on content analysis
     *
     * @param string $text The paragraph text to analyze
     * @return int The paragraph level (0 for main points, 1 for sub-points)
     */
    protected function determineParagraphLevel($text)
    {
        // Check for indicators of sub-points in legal documents
        $subPointIndicators = [
            'specifically',
            'in particular',
            'for example',
            'such as',
            'including',
            'namely',
            'to wit',       // Common in legal documents
            'inter alia',   // Common in legal documents
            'including but not limited to'
        ];

        // Check for explicit indicators at the start of the paragraph
        foreach ($subPointIndicators as $indicator) {
            if (stripos($text, $indicator) === 0) {
                return 1; // Sub-point (level 1)
            }
        }

        // Check for indentation in Quill content (if this is from Quill editor)
        if (preg_match('/^\s{4,}/', $text)) {
            return 1; // Indented text is likely a sub-point
        }

        // Check for contextual clues that this might be a sub-point
        if (
            strlen($text) < 100 && // Sub-points tend to be shorter
            !preg_match('/^[A-Z]/', $text) && // Sub-points often don't start with capital letters
            !preg_match('/\.$/', $text)
        ) { // Sub-points might not end with periods
            return 1;
        }

        // Check if this paragraph elaborates on a previous point
        // (This is a heuristic and might need adjustment)
        if (preg_match('/^(this|these|that|those|it|they)\s+(is|are|was|were|has|have|had)/', strtolower($text))) {
            return 1;
        }

        return 0; // Default to main point (level 0)
    }

    /**
     * Process causes of action section
     */
    protected function processCausesOfActionSection($section, $content, Draft $draft, $convertedData = null)
    {
        // Check if we should use formal formatting
        $useFormalFormatting = $this->shouldUseFormalFormatting($draft);

        // Add heading only for formal documents
        if ($useFormalFormatting) {
            $section->addText(
                'CAUSES OF ACTION',
                ['bold' => true, 'size' => 14],
                ['alignment' => 'center', 'spaceBefore' => 360, 'spaceAfter' => 240]
            );
        }

        // Create a numbering style for causes of action
        $phpWord = $section->getPhpWord();
        $numberingStyleName = 'numberingCausesOfAction';
        $phpWord->addNumberingStyle(
            $numberingStyleName,
            [
                'type' => 'multilevel',
                'levels' => [
                    [
                        'format' => 'decimal',
                        'text' => '%1.',
                        'left' => 360,
                        'hanging' => 360,
                        'tabPos' => 360
                    ],
                    [
                        'format' => 'lowerLetter',
                        'text' => '%2)',
                        'left' => 720,
                        'hanging' => 360,
                        'tabPos' => 720
                    ]
                ]
            ]
        );

        // Check if content is JSON and we don't have converted data yet
        if (!$convertedData && is_string($content) && $this->isValidJson($content)) {
            try {
                $jsonData = json_decode($content, true);

                // Check if it's a Quill Delta format
                if (isset($jsonData['ops']) && is_array($jsonData['ops'])) {
                    // Create HTML converter instance
                    $htmlConverter = new HtmlToDocumentConverter();
                    $convertedData = $htmlConverter->convert($content, 'causes_of_action');
                }
            } catch (\Exception $e) {
                // If JSON parsing fails, continue with existing data
            }
        }

        if ($convertedData && isset($convertedData['causes'])) {
            // Use the converted data
            $causes = $convertedData['causes'];

            foreach ($causes as $index => $cause) {
                // Add cause of action title
                if (!empty($cause['title'])) {
                    $section->addText(
                        ($index + 1) . '. ' . $cause['title'],
                        ['bold' => true, 'size' => 14],
                        ['alignment' => 'center', 'spaceBefore' => 240, 'spaceAfter' => 120]
                    );
                }

                // Process paragraphs
                foreach ($cause['paragraphs'] as $paragraph) {
                    // Skip empty paragraphs
                    if (empty(trim($paragraph['text']))) {
                        continue;
                    }

                    // Get formatting
                    $format = $paragraph['format'] ?? [];

                    // Set default formatting
                    $fontStyle = ['size' => 14];
                    $paraStyle = ['alignment' => 'both', 'lineHeight' => 2.0];

                    // Apply bold formatting if specified
                    if (isset($format['bold']) && $format['bold']) {
                        $fontStyle['bold'] = true;
                    }

                    // Apply italic formatting if specified
                    if (isset($format['italic']) && $format['italic']) {
                        $fontStyle['italic'] = true;
                    }

                    // Apply underline formatting if specified
                    if (isset($format['underline'])) {
                        $fontStyle['underline'] = $format['underline'];
                    }

                    // Apply indentation if specified
                    if (isset($format['indentation'])) {
                        $paraStyle['indentation'] = $format['indentation'];
                    }

                    // Apply alignment if specified
                    if (isset($format['alignment'])) {
                        $paraStyle['alignment'] = $format['alignment'];
                    }

                    // Check if this is a header
                    if (isset($format['header'])) {
                        $headerLevel = $format['header'];
                        $fontStyle['bold'] = true;

                        if ($headerLevel <= 2) {
                            $paraStyle['alignment'] = 'center';
                            $paraStyle['spaceBefore'] = 240;
                            $paraStyle['spaceAfter'] = 120;
                        }
                    }

                    // Add the text
                    $section->addText(
                        $paragraph['text'],
                        $fontStyle,
                        $paraStyle
                    );
                }
            }
        } else {
            // Check if content is Quill Delta JSON
            if (is_string($content) && $this->isValidJson($content)) {
                try {
                    $jsonData = json_decode($content, true);

                    // If it's Quill Delta format, try to extract causes of action
                    if (isset($jsonData['ops']) && is_array($jsonData['ops'])) {
                        // First, extract all text and split by lines
                        $allText = '';
                        foreach ($jsonData['ops'] as $op) {
                            if (isset($op['insert'])) {
                                $allText .= $op['insert'];
                            }
                        }

                        // Split by lines and process
                        $lines = preg_split('/\r\n|\r|\n/', $allText);
                        $causes = [];
                        $currentCause = null;
                        $currentParagraphs = [];

                        foreach ($lines as $line) {
                            $line = trim($line);

                            // Skip empty lines
                            if (empty($line)) {
                                continue;
                            }

                            // Check if this line is a cause of action title
                            if (preg_match('/^(FIRST|SECOND|THIRD|FOURTH|FIFTH|SIXTH|SEVENTH|EIGHTH|NINTH|TENTH|ELEVENTH|TWELFTH)\s+CAUSE\s+OF\s+ACTION/i', $line)) {
                                // Save previous cause if exists
                                if ($currentCause !== null && !empty($currentParagraphs)) {
                                    $currentCause['paragraphs'] = $currentParagraphs;
                                    $causes[] = $currentCause;
                                }

                                // Start new cause
                                $currentCause = [
                                    'title' => $line,
                                    'paragraphs' => []
                                ];
                                $currentParagraphs = [];
                            }
                            // Check for numbered cause format (1., 2., etc.)
                            else if (preg_match('/^(\d+)\.\s*(.+CAUSE\s+OF\s+ACTION.*)$/i', $line, $matches)) {
                                // Save previous cause if exists
                                if ($currentCause !== null && !empty($currentParagraphs)) {
                                    $currentCause['paragraphs'] = $currentParagraphs;
                                    $causes[] = $currentCause;
                                }

                                // Start new cause
                                $currentCause = [
                                    'title' => $matches[2], // Remove the number prefix
                                    'paragraphs' => []
                                ];
                                $currentParagraphs = [];
                            }
                            // Check for COUNT format
                            else if (preg_match('/^COUNT\s+(ONE|TWO|THREE|FOUR|FIVE|SIX|SEVEN|EIGHT|NINE|TEN|I{1,3}V?|V|VI{1,3}|IX|X|\d+)/i', $line)) {
                                // Save previous cause if exists
                                if ($currentCause !== null && !empty($currentParagraphs)) {
                                    $currentCause['paragraphs'] = $currentParagraphs;
                                    $causes[] = $currentCause;
                                }

                                // Start new cause
                                $currentCause = [
                                    'title' => $line,
                                    'paragraphs' => []
                                ];
                                $currentParagraphs = [];
                            }
                            // Regular paragraph content
                            else if ($currentCause !== null) {
                                $currentParagraphs[] = [
                                    'text' => $line,
                                    'format' => []
                                ];
                            }
                        }

                        // Add the last cause if not empty
                        if ($currentCause !== null && !empty($currentParagraphs)) {
                            $currentCause['paragraphs'] = $currentParagraphs;
                            $causes[] = $currentCause;
                        }

                        // If we found causes, process them
                        if (!empty($causes)) {
                            foreach ($causes as $index => $cause) {
                                // Add cause of action title
                                if (!empty($cause['title'])) {
                                    $section->addText(
                                        ($index + 1) . '. ' . $cause['title'],
                                        ['bold' => true, 'size' => 14],
                                        ['alignment' => 'center', 'spaceBefore' => 240, 'spaceAfter' => 120]
                                    );
                                }

                                // Process paragraphs
                                foreach ($cause['paragraphs'] as $paragraph) {
                                    // Skip empty paragraphs
                                    if (empty(trim($paragraph['text']))) {
                                        continue;
                                    }

                                    // Get formatting
                                    $format = $paragraph['format'] ?? [];

                                    // Set default formatting
                                    $fontStyle = ['size' => 14];
                                    // Get the line height from the document settings
                                    $lineHeight = $this->shouldUseFormalFormatting($draft) ? 2.0 : 1.0;
                                    $paraStyle = ['alignment' => 'both', 'lineHeight' => $lineHeight];

                                    // Apply formatting
                                    if (isset($format['bold']) && $format['bold']) {
                                        $fontStyle['bold'] = true;
                                    }
                                    if (isset($format['italic']) && $format['italic']) {
                                        $fontStyle['italic'] = true;
                                    }
                                    if (isset($format['underline'])) {
                                        $fontStyle['underline'] = $format['underline'];
                                    }
                                    if (isset($format['alignment'])) {
                                        $paraStyle['alignment'] = $format['alignment'];
                                    }
                                    if (isset($format['indentation'])) {
                                        $paraStyle['indentation'] = $format['indentation'];
                                    }

                                    // Check if this is a list item
                                    if (isset($format['list'])) {
                                        // Format as list item
                                        $isOrdered = ($format['list'] === 'ordered');
                                        $bullet = $isOrdered ? '1.' : '•';

                                        $section->addText(
                                            $bullet . ' ' . $paragraph['text'],
                                            $fontStyle,
                                            ['alignment' => 'both', 'indentation' => ['left' => 720, 'hanging' => 360], 'lineHeight' => $this->shouldUseFormalFormatting($draft) ? 2.0 : 1.0]
                                        );
                                    } else {
                                        // Regular paragraph
                                        $section->addText(
                                            $paragraph['text'],
                                            $fontStyle,
                                            $paraStyle
                                        );
                                    }
                                }
                            }

                            return;
                        }
                    }
                } catch (\Exception $e) {
                    // If JSON parsing fails, continue with text processing
                }
            }

            // Fall back to old method
            // Split content by cause of action
            $causes = $this->splitCausesOfAction($content);

            foreach ($causes as $index => $cause) {
                // Add cause of action title
                if (!empty($cause['title'])) {
                    $section->addText(
                        ($index + 1) . '. ' . $cause['title'],
                        ['bold' => true, 'size' => 14],
                        ['alignment' => 'center', 'spaceBefore' => 240, 'spaceAfter' => 120]
                    );
                }

                // Process paragraphs
                $paragraphs = preg_split('/\r\n|\r|\n/', $cause['content']);

                // Track if we're in a list
                $inList = false;
                $listItems = [];
                $isOrdered = false;

                foreach ($paragraphs as $paragraph) {
                    // Skip empty paragraphs
                    if (trim($paragraph) === '') {
                        // If we were in a list and hit an empty line, process the list
                        if ($inList && !empty($listItems)) {
                            $this->addListToSection($section, $listItems, $isOrdered, $draft);
                            $listItems = [];
                            $inList = false;
                        }
                        continue;
                    }

                    // Check if this is a numbered paragraph/list item
                    if (preg_match('/^\s*(\d+)\.?\s+(.+)$/', $paragraph, $matches)) {
                        $number = (int)$matches[1];
                        $text = $matches[2];

                        // If this is the first item or follows the sequence, treat as list
                        if (!$inList || $number === 1 || $number === count($listItems) + 1) {
                            $inList = true;
                            $isOrdered = true;
                            $listItems[] = $text;
                        } else {
                            // Not part of a sequence, treat as regular paragraph
                            if ($inList && !empty($listItems)) {
                                $this->addListToSection($section, $listItems, $isOrdered, $draft);
                                $listItems = [];
                                $inList = false;
                            }

                            $section->addText(
                                $paragraph,
                                ['size' => 14],
                                ['alignment' => 'both', 'lineHeight' => $this->shouldUseFormalFormatting($draft) ? 2.0 : 1.0]
                            );
                        }
                    }
                    // Check if this is a bullet list item
                    else if (preg_match('/^\s*[\*\-•]\s+(.+)$/', $paragraph, $matches)) {
                        $text = $matches[1];

                        // If we were in a numbered list, finish it first
                        if ($inList && $isOrdered && !empty($listItems)) {
                            $this->addListToSection($section, $listItems, $isOrdered, $draft);
                            $listItems = [];
                        }

                        $inList = true;
                        $isOrdered = false;
                        $listItems[] = $text;
                    } else {
                        // Regular paragraph - if we were in a list, finish it first
                        if ($inList && !empty($listItems)) {
                            $this->addListToSection($section, $listItems, $isOrdered, $draft);
                            $listItems = [];
                            $inList = false;
                        }

                        $section->addText(
                            $paragraph,
                            ['size' => 14],
                            ['alignment' => 'both', 'lineHeight' => $this->shouldUseFormalFormatting($draft) ? 2.0 : 1.0]
                        );
                    }
                }

                // Process any remaining list items
                if ($inList && !empty($listItems)) {
                    $this->addListToSection($section, $listItems, $isOrdered, $draft);
                }
            }
        }
    }

    /**
     * Process prayer for relief section
     */
    protected function processPrayerForReliefSection($section, $content, Draft $draft, $convertedData = null)
    {
        // Check if we should use formal formatting
        $useFormalFormatting = $this->shouldUseFormalFormatting($draft);

        // Add heading only for formal documents
        if ($useFormalFormatting) {
            $section->addText(
                'PRAYER FOR RELIEF',
                ['bold' => true, 'size' => 14],
                ['alignment' => 'center', 'spaceBefore' => 360, 'spaceAfter' => 240]
            );
        }

        // Create a numbering style for prayer points
        $phpWord = $section->getPhpWord();
        $numberingStyleName = 'numberingPrayerPoints';
        $phpWord->addNumberingStyle(
            $numberingStyleName,
            [
                'type' => 'multilevel',
                'levels' => [
                    [
                        'format' => 'decimal',
                        'text' => '%1.',
                        'left' => 360,
                        'hanging' => 360,
                        'tabPos' => 360
                    ],
                    [
                        'format' => 'lowerLetter',
                        'text' => '%2)',
                        'left' => 720,
                        'hanging' => 360,
                        'tabPos' => 720
                    ]
                ]
            ]
        );

        // Check if content is JSON and we don't have converted data yet
        if (!$convertedData && is_string($content) && $this->isValidJson($content)) {
            try {
                $jsonData = json_decode($content, true);

                // Check if it's a Quill Delta format
                if (isset($jsonData['ops']) && is_array($jsonData['ops'])) {
                    // Create HTML converter instance
                    $htmlConverter = new HtmlToDocumentConverter();
                    $convertedData = $htmlConverter->convert($content, 'prayer_for_relief');
                }
            } catch (\Exception $e) {
                // If JSON parsing fails, continue with existing data
            }
        }

        if ($convertedData && (isset($convertedData['hasWherefore']) || isset($convertedData['prayerPoints']))) {
            // Use the converted data
            $hasWherefore = $convertedData['hasWherefore'] ?? false;
            $whereforeText = $convertedData['whereforeText'] ?? '';
            $prayerPoints = $convertedData['prayerPoints'] ?? [];

            // Add the WHEREFORE clause if available
            if ($hasWherefore && !empty($whereforeText)) {
                $section->addText(
                    $whereforeText,
                    ['size' => 14],
                    ['alignment' => 'both', 'lineHeight' => $this->shouldUseFormalFormatting($draft) ? 2.0 : 1.0]
                );
            } else {
                // Add a standard WHEREFORE clause
                $section->addText(
                    'WHEREFORE, ' . ($draft->caseFile && isset($draft->caseFile->plaintiff_type) ?
                        ($draft->caseFile->plaintiff_type === 'plaintiff' ? 'Plaintiff' : 'Petitioner') : 'Plaintiff') .
                        ' respectfully requests that this Court:',
                    ['size' => 14],
                    ['alignment' => 'both', 'lineHeight' => $this->shouldUseFormalFormatting($draft) ? 2.0 : 1.0]
                );
            }

            // Process prayer points with proper numbering
            foreach ($prayerPoints as $index => $point) {
                // Skip empty points
                if (empty(trim($point))) {
                    continue;
                }

                // Determine paragraph level based on content
                $level = $this->determineParagraphLevel($point);

                // Add the prayer point with proper numbering
                $section->addListItem(
                    $point,
                    $level,
                    ['size' => 14],
                    $numberingStyleName,
                    ['alignment' => 'both', 'lineHeight' => $this->shouldUseFormalFormatting($draft) ? 2.0 : 1.0]
                );
            }

            // Add space after prayer points
            if (!empty($prayerPoints)) {
                $section->addText('', ['size' => 14], ['spacing' => 120]);
            }
        } else {
            // Check if content is Quill Delta JSON
            if (is_string($content) && $this->isValidJson($content)) {
                try {
                    $jsonData = json_decode($content, true);

                    // If it's Quill Delta format, extract prayer points
                    if (isset($jsonData['ops']) && is_array($jsonData['ops'])) {
                        $whereforeFound = false;
                        $whereforeText = '';
                        $prayerPoints = [];
                        $currentText = '';
                        $inList = false;

                        foreach ($jsonData['ops'] as $op) {
                            if (isset($op['insert'])) {
                                $text = $op['insert'];
                                $attributes = $op['attributes'] ?? [];

                                // Check for WHEREFORE text
                                if (!$whereforeFound && stripos($text, 'WHEREFORE') !== false) {
                                    $whereforeFound = true;
                                    $whereforeText = $text;
                                    continue;
                                }

                                // Check for list items
                                if ($text === "\n" && isset($attributes['list']) && $attributes['list'] === 'ordered') {
                                    if (!empty(trim($currentText))) {
                                        $prayerPoints[] = $currentText;
                                        $currentText = '';
                                    }
                                    $inList = true;
                                } else if ($text !== "\n") {
                                    $currentText .= $text;
                                } else {
                                    // Regular newline
                                    if ($inList && !empty(trim($currentText))) {
                                        $prayerPoints[] = $currentText;
                                        $currentText = '';
                                    }
                                    $inList = false;
                                }
                            }
                        }

                        // Add any remaining text
                        if ($inList && !empty(trim($currentText))) {
                            $prayerPoints[] = $currentText;
                        }

                        // Add WHEREFORE clause
                        if ($whereforeFound && !empty($whereforeText)) {
                            $section->addText(
                                $whereforeText,
                                ['size' => 14],
                                ['alignment' => 'both', 'lineHeight' => $this->shouldUseFormalFormatting($draft) ? 2.0 : 1.0]
                            );
                        } else {
                            // Add standard WHEREFORE clause
                            $section->addText(
                                'WHEREFORE, ' . ($draft->caseFile && isset($draft->caseFile->plaintiff_type) ?
                                    ($draft->caseFile->plaintiff_type === 'plaintiff' ? 'Plaintiff' : 'Petitioner') : 'Plaintiff') .
                                    ' respectfully requests that this Court:',
                                ['size' => 14],
                                ['alignment' => 'both', 'lineHeight' => $this->shouldUseFormalFormatting($draft) ? 2.0 : 1.0]
                            );
                        }

                        // Add prayer points
                        foreach ($prayerPoints as $index => $point) {
                            if (empty(trim($point))) continue;

                            $section->addText(
                                ($index + 1) . '. ' . $point,
                                ['size' => 14],
                                ['alignment' => 'both', 'indentation' => ['left' => 720, 'hanging' => 360], 'lineHeight' => $this->shouldUseFormalFormatting($draft) ? 2.0 : 1.0]
                            );
                        }

                        // Add space after prayer points
                        if (!empty($prayerPoints)) {
                            $section->addText('', ['size' => 14], ['spacing' => 120]);
                        }

                        return;
                    }
                } catch (\Exception $e) {
                    // If JSON parsing fails, continue with text processing
                }
            }

            // Fall back to old method for plain text
            // Check if content starts with "WHEREFORE"
            $hasWherefore = (stripos(trim($content), 'WHEREFORE') === 0);

            if ($hasWherefore) {
                // Find the end of the WHEREFORE clause
                $whereforeEnd = stripos($content, ':', 0);

                if ($whereforeEnd !== false) {
                    // Extract the WHEREFORE clause
                    $wherefore = substr($content, 0, $whereforeEnd + 1);

                    // Add the WHEREFORE clause
                    $section->addText(
                        $wherefore,
                        ['size' => 14],
                        ['alignment' => 'both', 'lineHeight' => $this->shouldUseFormalFormatting($draft) ? 2.0 : 1.0]
                    );

                    // Process the rest as prayer points
                    $prayers = substr($content, $whereforeEnd + 1);
                } else {
                    $prayers = $content;
                }
            } else {
                // Add a standard WHEREFORE clause
                $section->addText(
                    'WHEREFORE, ' . ($draft->caseFile && isset($draft->caseFile->plaintiff_type) ?
                        ($draft->caseFile->plaintiff_type === 'plaintiff' ? 'Plaintiff' : 'Petitioner') : 'Plaintiff') .
                        ' respectfully requests that this Court:',
                    ['size' => 14],
                    ['alignment' => 'both', 'lineHeight' => $this->shouldUseFormalFormatting($draft) ? 2.0 : 1.0]
                );

                $prayers = $content;
            }

            // Process prayer points with proper numbering
            $points = $this->extractPrayerPoints($prayers);

            foreach ($points as $point) {
                // Determine paragraph level based on content
                $level = $this->determineParagraphLevel($point);

                // Add the prayer point with proper numbering
                $section->addListItem(
                    $point,
                    $level,
                    ['size' => 14],
                    $numberingStyleName,
                    ['alignment' => 'both', 'lineHeight' => $this->shouldUseFormalFormatting($draft) ? 2.0 : 1.0]
                );
            }

            // Add space after prayer points
            if (!empty($points)) {
                $section->addText('', ['size' => 14], ['spacing' => 120]);
            }
        }
    }

    /**
     * Process body section (for letters and other documents)
     */
    protected function processBodySection($section, $content, Draft $draft, $convertedData = null)
    {
        // For body sections, we want proper paragraph spacing for letters

        if ($convertedData && isset($convertedData['paragraphs']) && !empty($convertedData['paragraphs'])) {
            // Process the body content with proper paragraph spacing
            foreach ($convertedData['paragraphs'] as $index => $paragraph) {
                // Skip empty paragraphs
                if (empty(trim($paragraph['text']))) {
                    continue;
                }

                // Get formatting
                $format = $paragraph['format'] ?? [];

                // Set default formatting for body text
                $fontStyle = ['size' => 14];
                $paraStyle = ['alignment' => 'both', 'lineHeight' => $this->lineHeight];

                // For letters, add spacing between paragraphs
                if ($this->isLetterDocument($draft) && $index > 0) {
                    $paraStyle['spaceBefore'] = 240; // Add space before each paragraph (except the first)
                }

                // Apply formatting
                if (isset($format['bold']) && $format['bold']) {
                    $fontStyle['bold'] = true;
                }
                if (isset($format['italic']) && $format['italic']) {
                    $fontStyle['italic'] = true;
                }
                if (isset($format['underline'])) {
                    $fontStyle['underline'] = $format['underline'];
                }
                if (isset($format['alignment'])) {
                    $paraStyle['alignment'] = $format['alignment'];
                }

                // Add the paragraph
                $section->addText(
                    $paragraph['text'],
                    $fontStyle,
                    $paraStyle
                );
            }
        } else {
            // Fallback to processing as regular text
            $this->processTextSection($section, $content, null, $convertedData);
        }
    }

    /**
     * Process header section (for letters and other documents)
     */
    protected function processHeaderSection($section, $content, Draft $draft, $convertedData = null)
    {
        // For header sections, we want to preserve line breaks within paragraphs
        // and use single spacing for letters

        if ($convertedData && isset($convertedData['paragraphs']) && !empty($convertedData['paragraphs'])) {
            // Process the header content with preserved line breaks
            foreach ($convertedData['paragraphs'] as $paragraph) {
                // Skip empty paragraphs
                if (empty(trim($paragraph['text']))) {
                    continue;
                }

                // Get formatting
                $format = $paragraph['format'] ?? [];

                // Set default formatting for headers
                $fontStyle = ['size' => 14];
                $paraStyle = ['alignment' => 'left', 'lineHeight' => $this->lineHeight];

                // Apply formatting
                if (isset($format['bold']) && $format['bold']) {
                    $fontStyle['bold'] = true;
                }
                if (isset($format['italic']) && $format['italic']) {
                    $fontStyle['italic'] = true;
                }
                if (isset($format['underline'])) {
                    $fontStyle['underline'] = $format['underline'];
                }
                if (isset($format['alignment'])) {
                    $paraStyle['alignment'] = $format['alignment'];
                }

                // For header sections, handle line breaks within the text
                $text = $paragraph['text'];

                // Split by line breaks and add each line separately to preserve formatting
                $lines = explode("\n", $text);
                foreach ($lines as $index => $line) {
                    if (!empty(trim($line))) {
                        $section->addText(
                            $line,
                            $fontStyle,
                            $paraStyle
                        );
                    } else if ($index < count($lines) - 1) {
                        // Add empty line for spacing, but only if it's not the last line
                        $section->addText('', $fontStyle, $paraStyle);
                    }
                }
            }

            // Add a horizontal line at the end of the header section for better readability
            $section->addLine(['weight' => 1, 'width' => 450, 'height' => 0, 'color' => '000000']);

            // Add some space after the line
            $section->addText('', ['size' => 14], ['spaceAfter' => 120]);
        } else {
            // Fallback to processing as regular text
            $this->processTextSection($section, $content, null, $convertedData);

            // Add horizontal line even for fallback processing
            $section->addLine(['weight' => 1, 'width' => 450, 'height' => 0, 'color' => '000000']);
            $section->addText('', ['size' => 14], ['spaceAfter' => 120]);
        }
    }

    /**
     * Process signature section
     */
    protected function processSignatureSection($section, $content, Draft $draft, $convertedData = null)
    {
        // Check if we have actual signature content to use
        $hasActualContent = false;

        // First, try to use the converted data if available
        if ($convertedData && isset($convertedData['paragraphs']) && !empty($convertedData['paragraphs'])) {
            $hasActualContent = true;

            // Process the actual signature content
            foreach ($convertedData['paragraphs'] as $paragraph) {
                // Skip empty paragraphs
                if (empty(trim($paragraph['text']))) {
                    continue;
                }

                // Get formatting
                $format = $paragraph['format'] ?? [];

                // Set default formatting
                $fontStyle = ['size' => 14];
                $paraStyle = ['alignment' => 'left', 'lineHeight' => $this->lineHeight];

                // Apply formatting
                if (isset($format['bold']) && $format['bold']) {
                    $fontStyle['bold'] = true;
                }
                if (isset($format['italic']) && $format['italic']) {
                    $fontStyle['italic'] = true;
                }
                if (isset($format['underline'])) {
                    $fontStyle['underline'] = $format['underline'];
                }
                if (isset($format['alignment'])) {
                    $paraStyle['alignment'] = $format['alignment'];
                }

                // Add the text
                $section->addText(
                    $paragraph['text'],
                    $fontStyle,
                    $paraStyle
                );
            }
        }
        // If no converted data, try to parse the content directly
        else if (!empty($content) && is_string($content)) {
            // Check if content is JSON (Quill Delta format)
            if ($this->isValidJson($content)) {
                try {
                    $jsonData = json_decode($content, true);

                    // If it's Quill Delta format, extract the text
                    if (isset($jsonData['ops']) && is_array($jsonData['ops'])) {
                        $hasActualContent = true;

                        // Create HTML converter instance
                        $htmlConverter = new HtmlToDocumentConverter();
                        $convertedData = $htmlConverter->convert($content, 'signature');

                        // Recursively call this method with the converted data
                        $this->processSignatureSection($section, $content, $draft, $convertedData);
                        return;
                    }
                } catch (\Exception $e) {
                    // If JSON parsing fails, continue with text processing
                }
            }

            // Process as plain text
            $lines = preg_split('/\r\n|\r|\n/', $content);
            foreach ($lines as $line) {
                $line = trim($line);
                if (!empty($line)) {
                    $hasActualContent = true;
                    $section->addText(
                        $line,
                        ['size' => 14],
                        ['alignment' => 'left', 'lineHeight' => $this->lineHeight]
                    );
                }
            }
        }

        // If we don't have actual content, fall back to default signature blocks based on document type
        if (!$hasActualContent) {
            if ($draft->draft_type === 'proposed_order') {
                // For proposed orders, use a judge signature block
                $section->addText(
                    'DATED this ___ day of ____________, 20___.',
                    ['size' => 14],
                    ['alignment' => 'left', 'spaceBefore' => 360]
                );

                $section->addText(
                    '',
                    ['size' => 14],
                    ['alignment' => 'left', 'spaceBefore' => 240]
                );

                $section->addText(
                    '_______________________________',
                    ['size' => 14],
                    ['alignment' => 'left']
                );

                $section->addText(
                    'JUDGE',
                    ['size' => 14],
                    ['alignment' => 'left']
                );
            } else if ($draft->draft_type === 'affidavit') {
                // For affidavits, use an affiant signature block
                $section->addText(
                    '_______________________________',
                    ['size' => 14],
                    ['alignment' => 'left', 'spaceBefore' => 360]
                );

                $section->addText(
                    'AFFIANT',
                    ['size' => 14],
                    ['alignment' => 'left']
                );
            } else if ($draft->draft_type === 'certificate_of_service') {
                // For certificates of service, use a standard signature block
                $section->addText(
                    'DATED this ___ day of ____________, 20___.',
                    ['size' => 14],
                    ['alignment' => 'left', 'spaceBefore' => 360]
                );

                $section->addText(
                    '',
                    ['size' => 14],
                    ['alignment' => 'left', 'spaceBefore' => 240]
                );

                $section->addText(
                    '_______________________________',
                    ['size' => 14],
                    ['alignment' => 'left']
                );
            } else {
                // For other document types, use the standard respectfully submitted line
                $section->addText(
                    'Respectfully submitted,',
                    ['bold' => true, 'size' => 14],
                    ['alignment' => 'left', 'spaceBefore' => 360]
                );

                // Add signature line
                $section->addText(
                    '_______________________________',
                    ['size' => 14],
                    ['alignment' => 'left']
                );
            }
        }


    }

    /**
     * Process a generic section
     */
    protected function processGenericSection($section, $draftSection, Draft $draft, $convertedData = null)
    {
        $content = $draftSection['content'] ?? '';
        $name = $draftSection['name'] ?? '';
        $useFormalFormatting = $this->shouldUseFormalFormatting($draft);

        // Add heading if name is provided and we're using formal formatting
        if (!empty($name) && $useFormalFormatting) {
            $section->addText(
                strtoupper($name),
                ['bold' => true, 'size' => 14],
                ['alignment' => 'center', 'spaceBefore' => 360, 'spaceAfter' => 240]
            );
        }

        // Process content as regular text
        $this->processTextSection($section, $content, null, $convertedData);
    }

    /**
     * Extract court information from content or case file
     */
    protected function extractCourtInfo($content, CaseFile $caseFile)
    {
        $info = [
            'courtName' => 'SUPERIOR COURT',
            'division' => '',
            'caseNumber' => $caseFile->case_number ?? '',
            'judge' => $caseFile->judge ?? ''
        ];

        // Try to extract court name from content
        if (preg_match('/IN THE\s+(.*?)(?:COURT|$)/i', $content, $matches)) {
            $info['courtName'] = trim($matches[1]);
        } else {
            // Use jurisdiction from case file if available
            if (!empty($caseFile->jurisdiction)) {
                $info['courtName'] = strtoupper($caseFile->jurisdiction) . ' COURT';
            }
        }

        // Try to extract division
        if (preg_match('/(?:DIVISION|DISTRICT):\s*(.*?)(?:\n|$)/i', $content, $matches)) {
            $info['division'] = trim($matches[1]);
        }

        // Try to extract case number
        if (preg_match('/CASE(?:\s+NO\.?|NUMBER):\s*(.*?)(?:\n|$)/i', $content, $matches)) {
            $info['caseNumber'] = trim($matches[1]);
        }

        // Try to extract judge
        if (preg_match('/(?:JUDGE|HON\.?):\s*(.*?)(?:\n|$)/i', $content, $matches)) {
            $info['judge'] = trim($matches[1]);
        }

        return $info;
    }

    /**
     * Extract parties from content or case file
     */
    protected function extractParties($content, CaseFile $caseFile)
    {
        $parties = [];

        // Try to extract from content first
        $lines = preg_split('/\r\n|\r|\n/', $content);
        $inParties = false;

        foreach ($lines as $line) {
            $line = trim($line);

            // Skip empty lines
            if (empty($line)) {
                if ($inParties) {
                    $parties[] = '';
                }
                continue;
            }

            // Check for party indicators
            if (
                stripos($line, 'plaintiff') !== false ||
                stripos($line, 'defendant') !== false ||
                stripos($line, 'petitioner') !== false ||
                stripos($line, 'respondent') !== false
            ) {
                $inParties = true;
                $parties[] = $line;
            } elseif ($inParties) {
                $parties[] = $line;
            }
        }

        // If we couldn't extract from content, use case file
        if (empty($parties)) {
            if (!empty($caseFile->plaintiff_name)) {
                $parties[] = $caseFile->plaintiff_name . ',';
                $parties[] = '';
                $parties[] = $caseFile->plaintiff_type ?? 'Plaintiff' . ',';
            }

            if (!empty($caseFile->plaintiff_name) && !empty($caseFile->defendant_name)) {
                $parties[] = '';
                $parties[] = 'v.';
                $parties[] = '';
            }

            if (!empty($caseFile->defendant_name)) {
                $parties[] = $caseFile->defendant_name . ',';
                $parties[] = '';
                $parties[] = $caseFile->defendant_type ?? 'Defendant' . ',';
            }
        }

        return $parties;
    }

    /**
     * Split causes of action into separate sections
     */
    protected function splitCausesOfAction($content)
    {
        $causes = [];
        $currentCause = null;

        // Split content by lines
        $lines = preg_split('/\r\n|\r|\n/', $content);

        foreach ($lines as $line) {
            // Check if this is a cause of action title
            if (
                preg_match('/^(?:COUNT|CAUSE|CLAIM)\s+(?:OF ACTION)?\s*(?:[IVX]+|\d+)?\s*:?\s*(.*?)$/i', $line, $matches) ||
                preg_match('/^(?:FIRST|SECOND|THIRD|FOURTH|FIFTH|SIXTH|SEVENTH|EIGHTH|NINTH|TENTH)\s+(?:CAUSE|COUNT|CLAIM)\s+(?:OF ACTION)?:?\s*(.*?)$/i', $line, $matches)
            ) {

                // Save previous cause if exists
                if ($currentCause !== null) {
                    $causes[] = $currentCause;
                }

                // Start new cause
                $currentCause = [
                    'title' => trim($matches[1] ?? $line),
                    'content' => ''
                ];
            } elseif ($currentCause !== null) {
                // Add to current cause content
                $currentCause['content'] .= $line . "\n";
            } else {
                // If no cause title found yet, create a default one
                $currentCause = [
                    'title' => '',
                    'content' => $content
                ];
            }
        }

        // Add the last cause
        if ($currentCause !== null) {
            $causes[] = $currentCause;
        }

        return $causes;
    }

    /**
     * Extract prayer points from content
     */
    protected function extractPrayerPoints($content)
    {
        $points = [];

        // Check if content already has numbered points
        if (preg_match_all('/^\s*\d+\.?\s+(.+)$/m', $content, $matches)) {
            $points = $matches[1];
        } else {
            // Split by semicolons or line breaks
            $splitContent = preg_split('/;|\r\n|\r|\n/', $content);

            foreach ($splitContent as $item) {
                $item = trim($item);
                if (!empty($item)) {
                    $points[] = $item;
                }
            }
        }

        return $points;
    }

    /**
     * Extract signature information
     */
    protected function extractSignatureInfo($content, Draft $draft)
    {
        $info = [];

        // Try to extract from content
        $lines = preg_split('/\r\n|\r|\n/', $content);
        $inSignature = false;

        foreach ($lines as $line) {
            $line = trim($line);

            // Skip empty lines
            if (empty($line)) {
                continue;
            }

            // Check for signature indicators
            if (
                stripos($line, 'respectfully') !== false ||
                stripos($line, 'submitted') !== false ||
                stripos($line, 'dated') !== false
            ) {
                $inSignature = true;
                continue;
            }

            if ($inSignature && $line !== '_______________________________') {
                $info[] = $line;
            }
        }

        // If we couldn't extract from content, use case file and user info
        if (empty($info)) {
            $user = $draft->creator;
            $caseFile = $draft->caseFile;

            if ($user) {
                $info[] = $user->name;

                if ($user->attorney_bar_number) {
                    $info[] = "Bar No. " . $user->attorney_bar_number;
                }

                if ($user->email) {
                    $info[] = $user->email;
                }

                if ($user->phone) {
                    $info[] = $user->phone;
                }
            } else {
                // Use plaintiff/petitioner info
                $info[] = $caseFile->plaintiff_name ?? 'Plaintiff';

                if (!empty($caseFile->plaintiff_address)) {
                    $addressParts = explode("\n", $caseFile->plaintiff_address);
                    $info = array_merge($info, $addressParts);
                }

                if (!empty($caseFile->plaintiff_email)) {
                    $info[] = $caseFile->plaintiff_email;
                }

                if (!empty($caseFile->plaintiff_phone)) {
                    $info[] = $caseFile->plaintiff_phone;
                }
            }
        }

        return $info;
    }

    /**
     * Process notary block section for affidavits
     */
    protected function processNotaryBlockSection($section, $content, Draft $draft, $convertedData = null)
    {
        // Add space before notary block
        $section->addText(
            '',
            ['size' => 14],
            ['alignment' => 'left', 'spaceBefore' => 360]
        );

        // Add state and county lines
        $section->addText(
            'STATE OF _________________ )',
            ['size' => 14],
            ['alignment' => 'left']
        );

        $section->addText(
            '                                                ) ss.',
            ['size' => 14],
            ['alignment' => 'left']
        );

        $section->addText(
            'COUNTY OF _______________ )',
            ['size' => 14],
            ['alignment' => 'left']
        );

        // Add subscription and sworn text
        $section->addText(
            '',
            ['size' => 14],
            ['alignment' => 'left', 'spaceBefore' => 240]
        );

        $section->addText(
            'SUBSCRIBED AND SWORN to before me this ___ day of ____________, 20___.',
            ['size' => 14],
            ['alignment' => 'left']
        );

        // Add notary signature line
        $section->addText(
            '',
            ['size' => 14],
            ['alignment' => 'left', 'spaceBefore' => 240]
        );

        $section->addText(
            '_______________________________',
            ['size' => 14],
            ['alignment' => 'left']
        );

        $section->addText(
            'NOTARY PUBLIC',
            ['size' => 14],
            ['alignment' => 'left']
        );

        $section->addText(
            'My Commission Expires: ___________',
            ['size' => 14],
            ['alignment' => 'left']
        );
    }

    /**
     * Generate a filename for the document
     */
    protected function generateFilename(Draft $draft)
    {
        $slug = Str::slug($draft->description ?? $draft->draft_type);
        $date = now()->format('Ymd_His');

        return "{$slug}_{$date}.docx";
    }

    /**
     * Process a document with exhibits
     *
     * @param string $documentPath Path to the main document
     * @param Draft $draft The draft
     * @param array $documentIds Array of document IDs to include as exhibits
     * @return void
     */
    protected function processDocumentWithExhibits(string $documentPath, Draft $draft, array $documentIds)
    {
        // Get the documents to use as exhibits
        $documents = \App\Models\Document::whereIn('id', $documentIds)
            ->get()
            ->sortBy(function ($document) use ($documentIds) {
                // Sort by the order in the documentIds array
                return array_search($document->id, $documentIds);
            });

        if ($documents->isEmpty()) {
            // If no documents found, return the original document path
            return str_replace(storage_path('app/public/'), '', $documentPath);
        }

        // Convert the main document to PDF
        $mainPdfPath = $this->convertDocxToPdf($documentPath);
        if (!$mainPdfPath) {
            // If conversion failed, return the original document path
            Log::error('Failed to convert main document to PDF', [
                'document_path' => $documentPath
            ]);
            return str_replace(storage_path('app/public/'), '', $documentPath);
        }

        // Create a new PDF with FPDI
        $pdf = new Fpdi();

        // Import the main document
        $pageCount = $pdf->setSourceFile($mainPdfPath);
        for ($i = 1; $i <= $pageCount; $i++) {
            $template = $pdf->importPage($i);
            $pdf->AddPage();
            $pdf->useTemplate($template);
        }

        // Process each document as an exhibit
        $exhibitLetter = 'A';
        foreach ($documents as $document) {
            // Add exhibit title page
            $pdf->AddPage();
            $pdf->SetFont('helvetica', 'B', 16);
            $title = $document->title ?: ($document->original_filename ?: 'Untitled');
            $pdf->Cell(0, 20, "Exhibit {$exhibitLetter}: {$title}", 0, 1, 'C');

            // Get the document path
            $documentPath = storage_path('app/' . $document->storage_path);
            if (!file_exists($documentPath)) {
                Log::warning('Document file not found', [
                    'document_id' => $document->id,
                    'path' => $documentPath
                ]);
                continue;
            }

            // If the document is a PDF, import its pages
            if ($document->mime_type === 'application/pdf') {
                try {
                    $documentPageCount = $pdf->setSourceFile($documentPath);
                    for ($i = 1; $i <= $documentPageCount; $i++) {
                        $template = $pdf->importPage($i);
                        $pdf->AddPage();
                        $pdf->useTemplate($template);
                    }
                } catch (\Exception $e) {
                    Log::error('Failed to import document PDF', [
                        'document_id' => $document->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }
            // If the document is a DOCX, convert to PDF first
            else if ($document->mime_type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
                $documentPdfPath = $this->convertDocxToPdf($documentPath);
                if ($documentPdfPath) {
                    try {
                        $documentPageCount = $pdf->setSourceFile($documentPdfPath);
                        for ($i = 1; $i <= $documentPageCount; $i++) {
                            $template = $pdf->importPage($i);
                            $pdf->AddPage();
                            $pdf->useTemplate($template);
                        }
                        // Clean up temporary PDF
                        unlink($documentPdfPath);
                    } catch (\Exception $e) {
                        Log::error('Failed to import converted document PDF', [
                            'document_id' => $document->id,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            }
            // For images, add them directly
            else if (strpos($document->mime_type, 'image/') === 0) {
                $pdf->AddPage();
                try {
                    $pdf->Image($documentPath, 10, 30, 190);
                } catch (\Exception $e) {
                    Log::error('Failed to add image document', [
                        'document_id' => $document->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }
            // For other file types, add a note
            else {
                $pdf->AddPage();
                $pdf->SetFont('helvetica', '', 12);
                $pdf->Cell(0, 20, "This exhibit is a {$document->mime_type} file and cannot be displayed inline.", 0, 1, 'C');
                $pdf->Cell(0, 20, "Please refer to the original file: {$document->original_filename}", 0, 1, 'C');
            }

            // Increment the exhibit letter
            $exhibitLetter++;
        }

        // Save the combined PDF
        $combinedFilename = pathinfo($documentPath, PATHINFO_FILENAME) . '_with_exhibits.pdf';
        $combinedPath = storage_path("app/public/documents/{$combinedFilename}");
        $pdf->Output($combinedPath, 'F');

        // Clean up temporary files
        unlink($mainPdfPath);
        unlink($documentPath);
    }

    /**
     * Convert a DOCX file to PDF
     *
     * @param string $docxPath Path to the DOCX file
     * @return string|false Path to the PDF file or false if conversion failed
     */
    protected function convertDocxToPdf(string $docxPath)
    {
        // Initialize the conversion service if not already done
        if (!$this->conversionService) {
            $this->conversionService = new DocumentConversionService();
        }

        // Use the conversion service to convert the document
        return $this->conversionService->convertDocxToPdf($docxPath);
    }

    /**
     * Check if a string is valid JSON
     */
    protected function isValidJson($string)
    {
        if (!is_string($string) || empty($string)) {
            return false;
        }

        json_decode($string);
        return (json_last_error() === JSON_ERROR_NONE);
    }
}
