<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ApiService
{
    /**
     * Make a POST request to the API
     *
     * @param string $endpoint
     * @param array $data
     * @return array
     */
    public function post($endpoint, array $data = [])
    {
        try {
            $response = Http::post(config('app.api_url') . $endpoint, $data);
            
            return $response->json();
        } catch (\Exception $e) {
            Log::error('API request failed: ' . $e->getMessage(), [
                'endpoint' => $endpoint,
                'data' => $data
            ]);
            
            throw $e;
        }
    }
    
    /**
     * Make a GET request to the API
     *
     * @param string $endpoint
     * @param array $params
     * @return array
     */
    public function get($endpoint, array $params = [])
    {
        try {
            $response = Http::get(config('app.api_url') . $endpoint, $params);
            
            return $response->json();
        } catch (\Exception $e) {
            Log::error('API request failed: ' . $e->getMessage(), [
                'endpoint' => $endpoint,
                'params' => $params
            ]);
            
            throw $e;
        }
    }
}