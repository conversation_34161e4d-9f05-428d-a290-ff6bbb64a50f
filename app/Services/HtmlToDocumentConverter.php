<?php

namespace App\Services;

use DOMDocument;
use DOMXPath;
use Illuminate\Support\Facades\Log;

class HtmlToDocumentConverter
{
    /**
     * Convert HTML content to structured data for document generation
     *
     * @param string $html The HTML content to convert
     * @param string|null $sectionType The type of section being converted
     * @return array|null Structured data for document generation
     */
    public function convert($content, $sectionType = null)
    {
        // Check if content is an EditorJS JSON string
        if ($this->isEditorJS($content)) {
            return $this->convertEditorJS($content, $sectionType);
        }

        // Check if content is a Quill Delta JSON string
        if ($this->isQuillDelta($content)) {
            return $this->convertQuillDelta($content, $sectionType);
        }

        // If it's not a Quill Delta or EditorJS, try to parse as HTML
        if ($this->isHtml($content)) {
            return $this->convertHtml($content, $sectionType);
        }

        // If it's plain text, convert to paragraphs
        return $this->convertPlainText($content, $sectionType);
    }

    /**
     * Check if content is an EditorJS JSON string
     */
    protected function isEditorJS($content)
    {
        if (!is_string($content)) {
            return false;
        }

        $content = trim($content);

        // Check if it starts with { and has "blocks" property
        if (substr($content, 0, 1) === '{') {
            try {
                $json = json_decode($content, true);
                return $json && isset($json['blocks']) && is_array($json['blocks']);
            } catch (\Exception $e) {
                return false;
            }
        }

        return false;
    }

    /**
     * Check if content is a Quill Delta JSON string
     */
    protected function isQuillDelta($content)
    {
        if (!is_string($content)) {
            return false;
        }

        $content = trim($content);

        // Check if it starts with { and has "ops" property
        if (substr($content, 0, 1) === '{') {
            try {
                $json = json_decode($content, true);
                return $json && isset($json['ops']) && is_array($json['ops']);
            } catch (\Exception $e) {
                return false;
            }
        }

        return false;
    }

    /**
     * Convert EditorJS JSON to structured data
     */
    protected function convertEditorJS($content, $sectionType = null)
    {
        try {
            $editorData = json_decode($content, true);

            if (!isset($editorData['blocks']) || !is_array($editorData['blocks'])) {
                return null;
            }

            $result = [
                'paragraphs' => [],
                'lists' => []
            ];

            foreach ($editorData['blocks'] as $block) {
                $blockType = $block['type'] ?? 'paragraph';
                $blockData = $block['data'] ?? [];
                $blockTunes = $block['tunes'] ?? [];

                // Process block based on type
                switch ($blockType) {
                    case 'paragraph':
                        $text = $blockData['text'] ?? '';
                        $format = [];

                        // Check for alignment in tunes
                        if (isset($blockTunes['alignment']) && isset($blockTunes['alignment']['alignment'])) {
                            $format['alignment'] = $blockTunes['alignment']['alignment'];
                        }

                        // Add paragraph to result
                        $result['paragraphs'][] = [
                            'text' => $text,
                            'format' => $format
                        ];
                        break;

                    case 'header':
                        $text = $blockData['text'] ?? '';
                        $level = $blockData['level'] ?? 2;
                        $format = ['header' => $level];

                        // Check for alignment in tunes
                        if (isset($blockTunes['alignment']) && isset($blockTunes['alignment']['alignment'])) {
                            $format['alignment'] = $blockTunes['alignment']['alignment'];
                        }

                        // Add header to result
                        $result['paragraphs'][] = [
                            'text' => $text,
                            'format' => $format
                        ];
                        break;

                    case 'list':
                        $items = $blockData['items'] ?? [];
                        $style = $blockData['style'] ?? 'unordered';

                        // Create list entry
                        $list = [
                            'items' => $items,
                            'ordered' => ($style === 'ordered'),
                            'level' => 0,
                            'format' => []
                        ];

                        // Check for alignment in tunes
                        if (isset($blockTunes['alignment']) && isset($blockTunes['alignment']['alignment'])) {
                            $list['format']['alignment'] = $blockTunes['alignment']['alignment'];
                        }

                        // Add list to result
                        $result['lists'][] = $list;
                        break;

                    case 'quote':
                        $text = $blockData['text'] ?? '';
                        $caption = $blockData['caption'] ?? '';
                        $format = ['italic' => true];

                        // Check for alignment in tunes
                        if (isset($blockTunes['alignment']) && isset($blockTunes['alignment']['alignment'])) {
                            $format['alignment'] = $blockTunes['alignment']['alignment'];
                        }

                        // Add quote to result
                        $result['paragraphs'][] = [
                            'text' => $text,
                            'format' => $format
                        ];

                        // Add caption if present
                        if (!empty($caption)) {
                            $result['paragraphs'][] = [
                                'text' => '— ' . $caption,
                                'format' => ['alignment' => 'right']
                            ];
                        }
                        break;

                    case 'table':
                        // Convert table to text representation
                        $tableContent = $this->convertTableToText($blockData);

                        // Add table content as paragraph
                        $result['paragraphs'][] = [
                            'text' => $tableContent,
                            'format' => []
                        ];
                        break;

                    default:
                        // For any other block type, try to extract text
                        if (isset($blockData['text'])) {
                            $result['paragraphs'][] = [
                                'text' => $blockData['text'],
                                'format' => []
                            ];
                        }
                        break;
                }
            }

            // Process section-specific data
            if ($sectionType) {
                $this->processSectionSpecificData($result, $sectionType);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Error converting EditorJS data', [
                'error' => $e->getMessage(),
                'content' => $content
            ]);
            return null;
        }
    }

    /**
     * Convert table data to text representation
     */
    protected function convertTableToText($tableData)
    {
        $content = '';
        $withHeadings = $tableData['withHeadings'] ?? false;
        $content = $tableData['content'] ?? [];

        if (empty($content)) {
            return '';
        }

        $result = '';

        // Process each row
        foreach ($content as $rowIndex => $row) {
            $rowText = '';

            // Process each cell
            foreach ($row as $cell) {
                $rowText .= $cell . "\t";
            }

            $result .= trim($rowText) . "\n";

            // Add separator after header row
            if ($withHeadings && $rowIndex === 0) {
                $result .= str_repeat('-', 40) . "\n";
            }
        }

        return $result;
    }

    /**
     * Convert Quill Delta JSON to structured data
     */
    protected function convertQuillDelta($content, $sectionType = null)
    {
        try {
            $delta = json_decode($content, true);

            if (!isset($delta['ops']) || !is_array($delta['ops'])) {
                return null;
            }

            $result = [
                'paragraphs' => [],
                'lists' => []
            ];

            $currentParagraph = [
                'text' => '',
                'format' => []
            ];

            $currentList = null;
            $currentListItems = [];

            foreach ($delta['ops'] as $op) {
                // Handle insert operations
                if (isset($op['insert'])) {
                    $text = $op['insert'];
                    $attributes = $op['attributes'] ?? [];

                    // Check if this is a newline with attributes (paragraph or list formatting)
                    if ($text === "\n") {
                        // Check if this is a list item
                        if (isset($attributes['list'])) {
                            $listType = $attributes['list']; // 'ordered' or 'bullet'

                            // If we have text in the current paragraph, add it to list items
                            if (!empty(trim($currentParagraph['text']))) {
                                $currentListItems[] = $currentParagraph['text'];

                                // Reset current paragraph
                                $currentParagraph = [
                                    'text' => '',
                                    'format' => []
                                ];
                            }

                            // Set current list type if not already set
                            if ($currentList === null) {
                                $currentList = [
                                    'ordered' => ($listType === 'ordered'),
                                    'level' => isset($attributes['indent']) ? (int)$attributes['indent'] : 0,
                                    'format' => []
                                ];

                                // Add alignment if specified
                                if (isset($attributes['align'])) {
                                    $currentList['format']['alignment'] = $attributes['align'];
                                }

                                // Add indentation based on level
                                $currentList['format']['indentation'] = [
                                    'left' => 720 + ($currentList['level'] * 360),
                                    'hanging' => 360
                                ];
                            }
                        } else {
                            // Not a list item, so if we have a current list, finalize it
                            if ($currentList !== null && !empty($currentListItems)) {
                                $currentList['items'] = $currentListItems;
                                $result['lists'][] = $currentList;

                                // Reset list tracking
                                $currentList = null;
                                $currentListItems = [];
                            }

                            // End of regular paragraph
                            if (!empty(trim($currentParagraph['text']))) {
                                $result['paragraphs'][] = $currentParagraph;
                            }

                            // Start a new paragraph
                            $currentParagraph = [
                                'text' => '',
                                'format' => []
                            ];

                            // Apply paragraph-level formatting
                            if (!empty($attributes)) {
                                foreach ($attributes as $key => $value) {
                                    switch ($key) {
                                        case 'header':
                                            $currentParagraph['format']['header'] = $value;
                                            break;
                                        case 'align':
                                            $currentParagraph['format']['alignment'] = $value;
                                            break;
                                        case 'indent':
                                            $currentParagraph['format']['indentation'] = [
                                                'left' => $value * 360 // 0.5 inch per indent level
                                            ];
                                            break;
                                    }
                                }
                            }
                        }
                    } else {
                        // Handle text with possible newlines
                        $lines = explode("\n", $text);

                        for ($i = 0; $i < count($lines); $i++) {
                            $line = $lines[$i];

                            // Add text to current paragraph
                            $currentParagraph['text'] .= $line;

                            // Apply character-level formatting
                            if (!empty($attributes)) {
                                foreach ($attributes as $key => $value) {
                                    switch ($key) {
                                        case 'bold':
                                            $currentParagraph['format']['bold'] = true;
                                            break;
                                        case 'italic':
                                            $currentParagraph['format']['italic'] = true;
                                            break;
                                        case 'underline':
                                            $currentParagraph['format']['underline'] = 'single';
                                            break;
                                    }
                                }
                            }

                            // If not the last line, handle line breaks based on section type
                            if ($i < count($lines) - 1) {
                                // For header and signature sections, preserve line breaks within the same paragraph
                                if ($sectionType === 'header' || $sectionType === 'signature') {
                                    $currentParagraph['text'] .= "\n";
                                } else {
                                    // For other sections, create separate paragraphs
                                    if (!empty(trim($currentParagraph['text']))) {
                                        $result['paragraphs'][] = $currentParagraph;
                                    }

                                    $currentParagraph = [
                                        'text' => '',
                                        'format' => []
                                    ];
                                }
                            }
                        }
                    }
                }
            }

            // Add the last paragraph if not empty
            if (!empty(trim($currentParagraph['text']))) {
                $result['paragraphs'][] = $currentParagraph;
            }

            // Add the last list if not empty
            if ($currentList !== null && !empty($currentListItems)) {
                $currentList['items'] = $currentListItems;
                $result['lists'][] = $currentList;
            }

            // Process section-specific data
            if ($sectionType) {
                $this->processSectionSpecificData($result, $sectionType);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Error converting Quill Delta', [
                'error' => $e->getMessage(),
                'content' => $content
            ]);
            return null;
        }
    }

    /**
     * Check if content is HTML
     */
    protected function isHtml($content)
    {
        if (!is_string($content)) {
            return false;
        }

        return strpos($content, '<') !== false && strpos($content, '>') !== false;
    }

    /**
     * Convert HTML to structured data
     */
    protected function convertHtml($html, $sectionType = null)
    {
        try {
            // Create a new DOM document
            $dom = new DOMDocument();

            // Load HTML, suppressing warnings for malformed HTML
            @$dom->loadHTML('<div>' . $html . '</div>', LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

            $xpath = new DOMXPath($dom);

            $result = [
                'paragraphs' => []
            ];

            // Process paragraphs
            $paragraphs = $xpath->query('//p | //h1 | //h2 | //h3 | //h4 | //h5 | //h6 | //div');

            foreach ($paragraphs as $p) {
                $text = $p->textContent;

                // Skip empty paragraphs
                if (empty(trim($text))) {
                    continue;
                }

                $format = [];

                // Determine paragraph format based on tag name
                $tagName = strtolower($p->nodeName);

                if (substr($tagName, 0, 1) === 'h') {
                    $format['header'] = (int)substr($tagName, 1, 1);
                }

                // Check for alignment
                try {
                    $style = $p->getAttribute('style');
                    if (!empty($style) && preg_match('/text-align:\s*(left|center|right|justify)/i', $style, $matches)) {
                        $format['alignment'] = $matches[1];
                    }
                } catch (\Exception $e) {
                    // Ignore attribute errors
                }

                // Check for bold, italic, underline
                if ($xpath->query('.//b | .//strong', $p)->length > 0) {
                    $format['bold'] = true;
                }

                if ($xpath->query('.//i | .//em', $p)->length > 0) {
                    $format['italic'] = true;
                }

                if ($xpath->query('.//u', $p)->length > 0) {
                    $format['underline'] = 'single';
                }

                $result['paragraphs'][] = [
                    'text' => $text,
                    'format' => $format
                ];
            }

            // Process section-specific data
            if ($sectionType) {
                $this->processSectionSpecificData($result, $sectionType);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Error converting HTML', [
                'error' => $e->getMessage(),
                'html' => $html
            ]);
            return null;
        }
    }

    /**
     * Convert plain text to structured data
     */
    protected function convertPlainText($text, $sectionType = null)
    {
        if (!is_string($text)) {
            return null;
        }

        $result = [
            'paragraphs' => []
        ];

        // Split text into paragraphs
        $paragraphs = preg_split('/\r\n|\r|\n/', $text);

        foreach ($paragraphs as $p) {
            // Skip empty paragraphs
            if (empty(trim($p))) {
                continue;
            }

            $result['paragraphs'][] = [
                'text' => $p,
                'format' => []
            ];
        }

        // Process section-specific data
        if ($sectionType) {
            $this->processSectionSpecificData($result, $sectionType);
        }

        return $result;
    }

    /**
     * Process section-specific data
     */
    protected function processSectionSpecificData(&$result, $sectionType)
    {
        switch ($sectionType) {
            case 'caption':
                $this->processCaptionData($result);
                break;

            case 'title':
                // No special processing needed
                break;

            case 'introduction':
                $this->processIntroductionData($result);
                break;

            case 'causes_of_action':
                $this->processCausesOfActionData($result);
                break;

            case 'prayer_for_relief':
                $this->processPrayerForReliefData($result);
                break;

            case 'signature':
                $this->processSignatureData($result);
                break;
        }
    }

    /**
     * Process caption section data
     */
    protected function processCaptionData(&$result)
    {
        // Extract court information
        $courtInfo = [
            'courtName' => '',
            'division' => ''
        ];

        // Extract parties
        $parties = [];

        // Look for court name in first few paragraphs
        for ($i = 0; $i < min(3, count($result['paragraphs'])); $i++) {
            $text = $result['paragraphs'][$i]['text'];

            if (preg_match('/IN THE\s+(.*?)(?:COURT|$)/i', $text, $matches)) {
                $courtInfo['courtName'] = trim($matches[1]);
                break;
            }
        }

        // Look for division
        for ($i = 0; $i < min(5, count($result['paragraphs'])); $i++) {
            $text = $result['paragraphs'][$i]['text'];

            if (preg_match('/(?:DIVISION|DISTRICT):\s*(.*?)(?:\n|$)/i', $text, $matches)) {
                $courtInfo['division'] = trim($matches[1]);
                break;
            }
        }

        // Extract parties from paragraphs
        $partySection = false;
        foreach ($result['paragraphs'] as $paragraph) {
            $text = $paragraph['text'];

            // Check if this paragraph contains party information
            if (preg_match('/plaintiff|defendant|petitioner|respondent/i', $text)) {
                $partySection = true;
                $parties[] = $text;
            } else if ($partySection && !empty(trim($text))) {
                // Continue adding lines as long as they're not empty
                $parties[] = $text;
            }
        }

        $result['courtInfo'] = $courtInfo;
        $result['parties'] = $parties;
    }

    /**
     * Process introduction section data
     */
    protected function processIntroductionData(&$result)
    {
        // Check for "COMES NOW" style
        $comesNowStyle = false;

        foreach ($result['paragraphs'] as $paragraph) {
            if (
                stripos($paragraph['text'], 'COMES NOW') !== false ||
                stripos($paragraph['text'], 'COME NOW') !== false
            ) {
                $comesNowStyle = true;
                break;
            }
        }

        $result['comesNowStyle'] = $comesNowStyle;
    }

    /**
     * Process causes of action section data
     */
    protected function processCausesOfActionData(&$result)
    {
        $causes = [];
        $currentCause = null;

        foreach ($result['paragraphs'] as $paragraph) {
            $text = $paragraph['text'];
            $format = $paragraph['format'];

            // Check if this is a cause of action title
            $isCauseTitle = false;

            if (isset($format['header']) || isset($format['bold'])) {
                $isCauseTitle = true;
            } else if (preg_match('/^(?:COUNT|CAUSE)\s+(?:OF\s+ACTION)?\s*(?:[IVX]+|\d+)/i', $text)) {
                $isCauseTitle = true;
            }

            if ($isCauseTitle) {
                // Save previous cause if exists
                if ($currentCause !== null) {
                    $causes[] = $currentCause;
                }

                // Start new cause
                $currentCause = [
                    'title' => $text,
                    'paragraphs' => []
                ];
            } else if ($currentCause !== null) {
                // Add paragraph to current cause
                $currentCause['paragraphs'][] = [
                    'text' => $text,
                    'format' => $format
                ];
            }
        }

        // Add the last cause
        if ($currentCause !== null) {
            $causes[] = $currentCause;
        }

        $result['causes'] = $causes;
    }

    /**
     * Process prayer for relief section data
     */
    protected function processPrayerForReliefData(&$result)
    {
        // Check for WHEREFORE clause
        $hasWherefore = false;
        $whereforeText = '';
        $prayerPoints = [];

        // First check paragraphs for WHEREFORE
        foreach ($result['paragraphs'] as $paragraph) {
            $text = $paragraph['text'];

            if (stripos($text, 'WHEREFORE') !== false) {
                $hasWherefore = true;
                $whereforeText = $text;
                break;
            }
        }

        // Extract prayer points from lists
        if (isset($result['lists']) && !empty($result['lists'])) {
            foreach ($result['lists'] as $list) {
                if ($list['ordered'] && !empty($list['items'])) {
                    $prayerPoints = array_merge($prayerPoints, $list['items']);
                }
            }
        }

        // If no prayer points found in lists, try to extract from paragraphs
        if (empty($prayerPoints)) {
            $inPrayer = $hasWherefore; // Start collecting after WHEREFORE if found

            foreach ($result['paragraphs'] as $paragraph) {
                $text = $paragraph['text'];

                // Skip the WHEREFORE paragraph
                if (stripos($text, 'WHEREFORE') !== false) {
                    continue;
                }

                // If we're in prayer section or paragraph looks like a prayer point
                if ($inPrayer || preg_match('/^\s*\d+\.?\s+(.+)$/', $text, $matches)) {
                    $inPrayer = true;

                    // Check if it's a numbered point
                    if (preg_match('/^\s*\d+\.?\s+(.+)$/', $text, $matches)) {
                        $prayerPoints[] = $matches[1];
                    } else {
                        $prayerPoints[] = $text;
                    }
                }
            }
        }

        $result['hasWherefore'] = $hasWherefore;
        $result['whereforeText'] = $whereforeText;
        $result['prayerPoints'] = $prayerPoints;
    }

    /**
     * Process signature section data
     */
    protected function processSignatureData(&$result)
    {
        $signatureInfo = [];
        $inSignature = false;

        foreach ($result['paragraphs'] as $paragraph) {
            $text = $paragraph['text'];

            // Check if this paragraph is part of the signature block
            if (
                !$inSignature &&
                (stripos($text, 'Respectfully') !== false ||
                    stripos($text, 'Submitted') !== false ||
                    stripos($text, 'Dated') !== false)
            ) {
                $inSignature = true;
            } else if ($inSignature && !empty(trim($text))) {
                $signatureInfo[] = $text;
            }
        }

        $result['signatureInfo'] = $signatureInfo;
    }
}
