<?php

namespace App\Services\Translation;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GeminiTranslationService
{
    /**
     * Translate content using Google Gemini API
     *
     * @param string $content The content to translate
     * @param string $targetLanguage The target language code (e.g., 'es', 'fr')
     * @return string The translated content
     * @throws \Exception If translation fails
     */
    public function translate(string $content, string $targetLanguage, string $sourceLanguage = ""): string
    {
        if (empty($content)) {
            return $content;
        }

        // Skip translation if target language is English
         if ($sourceLanguage) {
            $sourceLanguage = " Source language is " . $this->getLanguageName($sourceLanguage) . ".";
         }

        Log::info('Translating content to ' . $targetLanguage, [
            'content_length' => strlen($content),
            'target_language' => $targetLanguage
        ]);

        try {
            // Prepare the prompt for Gemini
            $prompt = "Translate the following markdown content to " . $this->getLanguageName($targetLanguage) .
                ". Preserve all markdown formatting, links, and structure. Do not add any explanatory text or notes. " .
                $sourceLanguage .
                "Return only the translated content:\n\n" . $content;

            // Call the Google Gemini API
            $apiKey = env('GOOGLE_GEMINI_API_KEY');
            $apiUrl = 'https://generativelanguage.googleapis.com/v1/models/gemini-1.5-pro:generateContent';

            $response = Http::withHeaders([
                'Content-Type' => 'application/json'
            ])->post($apiUrl . '?key=' . $apiKey, [
                'contents' => [
                    [
                        'parts' => [
                            ['text' => $prompt]
                        ]
                    ]
                ],
                'generationConfig' => [
                    'temperature' => 0.2,
                    'topP' => 0.8,
                    'topK' => 40,
                    'maxOutputTokens' => 8192,
                ]
            ]);

            if (!$response->successful()) {
                $errorData = $response->json();
                $errorMessage = $errorData['error']['message'] ?? 'Unknown error';
                $errorCode = $errorData['error']['code'] ?? $response->status();

                Log::error('Google Gemini API translation request failed', [
                    'status' => $response->status(),
                    'error_code' => $errorCode,
                    'error_message' => $errorMessage
                ]);

                throw new \Exception('Google Gemini API translation request failed: ' . $errorCode . ' - ' . $errorMessage);
            }

            $responseData = $response->json();

            // Extract the translated text from the response
            if (isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
                $translatedContent = $responseData['candidates'][0]['content']['parts'][0]['text'];

                Log::info('Translation completed successfully', [
                    'original_length' => strlen($content),
                    'translated_length' => strlen($translatedContent)
                ]);

                return $translatedContent;
            } else {
                throw new \Exception('Unexpected response format from Google Gemini API');
            }
        } catch (\Exception $e) {
            Log::error('Failed to translate content with Google Gemini API', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return original content if translation fails
            return $content;
        }
    }

    /**
     * Get the full language name from the language code
     *
     * @param string $languageCode The language code (e.g., 'es', 'fr')
     * @return string The full language name
     */
    private function getLanguageName(string $languageCode): string
    {
        $languages = [
            'en' => 'English',
            'es' => 'Spanish',
            'fr' => 'French',
            'de' => 'German',
            'it' => 'Italian',
            'pt' => 'Portuguese',
            'ru' => 'Russian',
            'zh' => 'Chinese',
            'ja' => 'Japanese',
            'ko' => 'Korean',
            'ar' => 'Arabic',
            'hi' => 'Hindi',
            'bn' => 'Bengali',
            'pa' => 'Punjabi',
            'ta' => 'Tamil',
            'te' => 'Telugu',
            'mr' => 'Marathi',
            'ur' => 'Urdu',
            'gu' => 'Gujarati',
            'kn' => 'Kannada',
            'ml' => 'Malayalam',
            'or' => 'Odia',
            'as' => 'Assamese',
            'nl' => 'Dutch',
            'tr' => 'Turkish',
            'pl' => 'Polish',
            'uk' => 'Ukrainian',
            'cs' => 'Czech',
            'sv' => 'Swedish',
            'no' => 'Norwegian',
            'da' => 'Danish',
            'fi' => 'Finnish',
            'el' => 'Greek',
            'hu' => 'Hungarian',
            'ro' => 'Romanian',
            'bg' => 'Bulgarian',
            'hr' => 'Croatian',
            'sr' => 'Serbian',
            'sk' => 'Slovak',
            'sl' => 'Slovenian',
            'lt' => 'Lithuanian',
            'lv' => 'Latvian',
            'et' => 'Estonian',
            'vi' => 'Vietnamese',
            'th' => 'Thai',
            'id' => 'Indonesian',
            'ms' => 'Malay',
            'fil' => 'Filipino',
            'he' => 'Hebrew',
            'fa' => 'Persian',
        ];

        return $languages[$languageCode] ?? 'the target language';
    }
}
