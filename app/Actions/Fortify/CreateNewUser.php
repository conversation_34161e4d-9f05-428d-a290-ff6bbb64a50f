<?php

namespace App\Actions\Fortify;

use App\Models\Team;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Laravel\Fortify\Contracts\CreatesNewUsers;
use <PERSON><PERSON>\Jetstream\Jetstream;

class CreateNewUser implements CreatesNewUsers
{
    use PasswordValidationRules;

    /**
     * Create a newly registered user.
     *
     * @param  array<string, string>  $input
     */
    public function create(array $input): User
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'username' => ['required', 'string', 'alpha_dash', 'min:3', 'max:25', 'unique:users'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => $this->passwordRules(),
            'is_attorney' => ['boolean'],
            'zip_code' => ['required', 'string', 'min:5', 'max:10'],
            'terms' => Jetstream::hasTermsAndPrivacyPolicyFeature() ? ['accepted', 'required'] : '',
        ];

        // Add bar card number validation if user is an attorney
        if (isset($input['is_attorney']) && $input['is_attorney']) {
            $rules['bar_card_number'] = ['required', 'string', 'min:4', 'max:20'];
        }

        Validator::make($input, $rules)->validate();

        return DB::transaction(function () use ($input) {
            $userData = [
                'name' => $input['name'],
                'username' => $input['username'],
                'email' => $input['email'],
                'password' => Hash::make($input['password']),
                'is_attorney' => isset($input['is_attorney']) ? $input['is_attorney'] : false,
                'zip_code' => $input['zip_code'],
                'latitude' => $input['latitude'] ?? null,
                'longitude' => $input['longitude'] ?? null,
            ];

            // Add bar card number if user is an attorney
            if (isset($input['is_attorney']) && $input['is_attorney'] && isset($input['bar_card_number'])) {
                $userData['bar_card_number'] = $input['bar_card_number'];
            }

            return tap(User::create($userData), function (User $user) {
                $this->createTeam($user);
            });
        });
    }

    /**
     * Create a personal team for the user.
     */
    protected function createTeam(User $user): void
    {
        $user->ownedTeams()->save(Team::forceCreate([
            'user_id' => $user->id,
            'name' => explode(' ', $user->name, 2)[0]."'s Team",
            'personal_team' => true,
        ]));
    }
}
