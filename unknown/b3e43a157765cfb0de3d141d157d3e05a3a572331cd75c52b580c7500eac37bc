<?php

namespace App\Jobs;

use App\Models\Document;
use App\Models\Party;
use App\Services\OpenAI\CaseAssistantService;
use App\Services\DocumentConversionService;
use App\Services\CreditService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use OpenAI\Laravel\Facades\OpenAI;
use SimpleXMLElement;
use ZipArchive;

class ProcessExhibitJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;
    public $backoff = [10, 60, 120];
    public $timeout = 60;

    private CaseAssistantService $assistantService;
    private ?DocumentConversionService $conversionService = null;
    private CreditService $creditService;

    public function __construct(
        private Document $document
    ) {
        $this->assistantService = app(CaseAssistantService::class);
        $this->conversionService = app(DocumentConversionService::class);
        $this->creditService = app(CreditService::class);
    }

    /**
     * Deduct credits for AI processing operations
     *
     * @param string $description Description of the operation
     * @return void
     */
    private function deductCreditsForAiProcessing(string $description): void
    {
        $user = $this->document->caseFile->user;
        if ($user) {
            $this->creditService->deductCredits(
                $user,
                5, // Deduct 5 credits for each AI operation
                $description,
                ['document_id' => $this->document->id, 'case_id' => $this->document->case_file_id]
            );
        }
    }

    /**
     * @throws \Exception
     */
    private function configureOpenAI(): void
    {
        $this->assistantService->configureCaseCredentials($this->document->caseFile);
    }

    public function handle(): void
    {
        try {
            $this->configureOpenAI();

            // Check if this is an audio, video, or image file
            $isAudioFile = str_starts_with($this->document->mime_type, 'audio/');
            $isVideoFile = str_starts_with($this->document->mime_type, 'video/');
            $isImageFile = str_starts_with($this->document->mime_type, 'image/');
            $isMediaFile = $isAudioFile || $isVideoFile || $isImageFile;

            if ($isMediaFile) {
                // For media files, skip OpenAI file upload
                $this->document->update([
                    'ingestion_status' => 'processing',
                    'skip_vector_store' => true
                ]);

                // Handle different media types
                if ($isAudioFile) {
                    $this->transcribeAudioFile()
                        ->generateSummaryIfNeeded();
                } else if ($isImageFile) {
                    $this->generateSummaryIfNeeded();
                } else {
                    // For video files, just generate a basic summary without transcription
                    $this->document->update([
                        'title' => $this->document->title ?: $this->document->original_filename,
                        'description' => $this->document->description ?: 'Video file uploaded. No transcription available.',
                        'ingestion_status' => 'completed'
                    ]);
                }
            } else {
                // For text documents, follow the normal flow
                $this->uploadToOpenAI()
                    ->generateSummaryIfNeeded()
                    ->attachToVectorStore();
            }
        } catch (\Exception $e) {
            Log::error('Document processing failed', [
                'document_id' => $this->document->id,
                'error' => $e->getMessage()
            ]);

            $this->document->update([
                'ingestion_status' => 'failed',
                'ingestion_error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    private function uploadToOpenAI(): self
    {
        $this->configureOpenAI();
        $this->document->update(['ingestion_status' => 'uploading']);

        Storage::disk('s3')->setVisibility($this->document->storage_path, 'public');

        $url = Storage::disk('s3')->url(
            $this->document->storage_path
        );

        try {// Upload to OpenAI
            $response = OpenAI::files()->upload([
                'purpose' => 'assistants',
                'file'    => fopen($url, 'r'),
            ]);

        } catch (\Exception $e) {
            throw new \Exception('Failed to upload document to OpenAI: ' . $e->getMessage());
        } finally {
            Storage::disk('s3')->setVisibility($this->document->storage_path, 'private');
        }


        // Update document record with OpenAI File ID
        $this->document->update(['openai_file_id' => $response->id]);

        return $this;
    }

    private function generateSummaryIfNeeded(): self
    {
        if ($this->document->title && $this->document->description) {
            return $this;
        }

        $this->configureOpenAI();
        $this->document->update(['ingestion_status' => 'summarizing']);

        try {
            // Catchall for any image type
            if (str_starts_with($this->document->mime_type, 'image/')) {
                // Use Google Gemini Vision to analyze the image
                $content = $this->analyzeDocumentWithGeminiVision();

                if (json_last_error() !== JSON_ERROR_NONE) {
                    Log::error('JSON decode error', [
                        'error' => json_last_error_msg(),
                        'raw_content' => $content
                    ]);
                    throw new \Exception('Failed to decode JSON response: ' . json_last_error_msg());
                }

                // Skip vector store for images
                $this->document->update(['skip_vector_store' => true]);

            } elseif (str_starts_with($this->document->mime_type, 'audio/')) {
                // Process audio files with transcription
                $this->transcribeAudioFile();

            } elseif (str_starts_with($this->document->mime_type, 'video/')) {
                // For video files, just set a basic title/description without transcription
                $this->document->update([
                    'title' => $this->document->title ?: $this->document->original_filename,
                    'description' => 'Video file uploaded. No transcription available.',
                    'skip_vector_store' => true,
                    'ingestion_status' => 'completed'
                ]);

                return $this; // Return early for video files
            } else {
                switch ($this->document->mime_type) {
                    case 'application/pdf':
                    case 'application/msword':
                        // Use Google Gemini to analyze the document
                        $content = $this->analyzeDocumentWithGemini();
                        break;
                    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                        // For DOCX files, extract text first and then analyze
                        $content = $this->analyzeDocxDocument();

                        // Validate the JSON structure
                        if (!isset($content['title']) || !isset($content['summary'])) {
                            Log::error('Invalid response format from Google Gemini', ['response' => $content]);
                            throw new \Exception('Google Gemini response missing required fields');
                        }

                        $this->document->update([
                            'title' => $content['title'],
                            'description' => $content['summary']
                        ]);

                        // Handle extracted parties if present
                        if (isset($content['parties']) && is_array($content['parties'])) {
                            Party::createFromDocumentAnalysis(
                                $content['parties'],
                                $this->document->caseFile->user_id
                            );
                        }

                        break;

                    default:
                        // For other file types, set a generic title if none exists
                        if (!$this->document->title) {
                            $this->document->update([
                                'title' => $this->document->original_filename,
                                'skip_vector_store' => true
                            ]);
                        }
                        break;
                }
            }

            // Validate the JSON structure
            if (!isset($content['title']) || !isset($content['summary'])) {
                Log::error('Invalid response format from OpenAI', ['response' => $content]);
                throw new \Exception('OpenAI response missing required fields');
            }

            $this->document->update([
                'title' => $content['title'],
                'description' => $content['summary']
            ]);

            // Deduct credits for AI title & summary generation
            $this->deductCreditsForAiProcessing('Title and summary generated for document');

            // Handle extracted parties if present
            if (isset($content['parties']) && is_array($content['parties'])) {
                Party::createFromDocumentAnalysis(
                    $content['parties'],
                    $this->document->caseFile->user_id
                );
            }

            return $this;
        } catch (\Exception $e) {
            throw new \Exception('Failed to generate document summary: ' . $e->getMessage());
        }
    }

    /**
     * Transcribe audio or video file and generate summary
     *
     * @return self
     * @throws \Exception
     */
    /**
     * Analyze document content using Google Gemini API
     *
     * @return array Parsed JSON response with title, summary, and optional parties
     * @throws \Exception If the API call fails or returns invalid data
     */


// Assuming necessary imports:
// use Illuminate\Support\Facades\Http;
// use Illuminate\Support\Facades\Log;
// use Illuminate\Support\Facades\Storage;
// use Illuminate\Support\Str; // For base64_encode

    private function analyzeDocumentWithGemini(): array
    {
        Log::info('Analyzing document with Google Gemini API', [
            'document_id' => $this->document->id,
            'mime_type'   => $this->document->mime_type
        ]);

        $this->document->update(['ingestion_status' => 'summarizing']);

        try {
            // 1. Fetch the actual document content from S3
            $fileContent = Storage::disk('s3')->get(
                $this->document->storage_path
            );
            if ($fileContent === null) {
                throw new \Exception(
                    'Failed to retrieve document content from S3.'
                );
            }

            // 2. Base64 encode the content
            $base64EncodedContent = base64_encode($fileContent);
            $mimeType
                = $this->document->mime_type; // Make sure this is an accurate MIME type supported by Gemini

            // 3. Prepare the prompt (No need to include URL/Filename here)
            $promptText
                = "Please analyze the provided document and return valid JSON only, in the format:\n"
                . "{\n"
                . "    \"title\": \"...\",\n"
                . "    \"summary\": \"...\",\n"
                . "    \"parties\": [\n"
                . "        {\n"
                . "            \"name\": \"...\",\n"
                . "            \"address_line1\": \"...\",\n"
                . "            \"address_line2\": null,\n"
                . "            \"city\": \"...\",\n"
                . "            \"state\": \"...\",\n"
                . "            \"zip\": \"...\",\n"
                . "            \"email\": null,\n"
                . "            \"phone\": null,\n"
                . "            \"relationship\": \"...\" /* one of: attorney, court, opponent, or omit if unsure */\n"
                . "        }\n"
                . "    ]\n"
                . "}\n\n"
                . "For the title and summary, be specific about the content. "
                . "For parties, only include entries where you are confident about the name and address. Ignore uncertain ones. Do not invent information.";


            // 4. Prepare the API request payload with multimodal input
            $apiKey = env('GOOGLE_GEMINI_API_KEY');
            $apiUrl
                = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-latest:generateContent'; // Using v1beta for potential latest features, check documentation
            // Or stick to v1 if preferred: 'https://generativelanguage.googleapis.com/v1/models/gemini-1.5-pro:generateContent';

            $payload = [
                'contents'         => [
                    [
                        'parts' => [
                            // Part 1: The text prompt
                            ['text' => $promptText],
                            // Part 2: The document data
                            [
                                'inlineData' => [
                                    'mimeType' => $mimeType,
                                    'data'     => $base64EncodedContent
                                ]
                            ]
                        ]
                    ]
                ],
                'generationConfig' => [
                    'responseMimeType' => 'application/json',
                    // Explicitly request JSON output
                    'temperature'      => 0.4,
                    'topP'             => 0.8,
                    'topK'             => 40,
                    'maxOutputTokens'  => 2048,
                    // Adjust if needed based on expected output size
                ]
                // Consider adding 'safetySettings' if needed
            ];

            // 5. Call the Google Gemini API
            $response = Http::withHeaders([
                'Content-Type' => 'application/json'
            ])->post($apiUrl . '?key=' . $apiKey, $payload);

            // --- Rest of your error handling and response parsing logic ---
            // (This part remains largely the same, but ensure you handle the case where Gemini
            // might still struggle to return perfect JSON despite the prompt)

            if (!$response->successful()) {
                // ... (your existing error handling) ...
                $errorData = $response->json();
                $errorMessage = $errorData['error']['message'] ??
                    'Unknown error';
                $errorCode = $errorData['error']['code'] ?? $response->status();
                Log::error('Google Gemini API request failed', [ /* ... */]);
                throw new \Exception(
                    'Google Gemini API request failed: ' . $errorCode . ' - '
                    . $errorMessage
                );
            }

            $responseData = $response->json();
            $rawContent = '';

            // Check the structure carefully based on the API version and if JSON output type was requested
            if (isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
                // If responseMimeType was 'application/json', the model *should* output only JSON text here.
                // If not explicitly set, it might still include text explanations around the JSON.
                $rawContent
                    = $responseData['candidates'][0]['content']['parts'][0]['text'];
            } else {
                Log::error(
                    'Unexpected response format from Google Gemini API',
                    ['response' => $responseData]
                );
                throw new \Exception(
                    'Unexpected response format from Google Gemini API'
                );
            }

            // Remove potential markdown backticks (your existing code is fine here)
            $rawContent = preg_replace(
                '/^```(?:json)?\s*|\s*```$/s',
                '',
                $rawContent
            ); // Trim whitespace too

            $content = json_decode($rawContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('JSON decode error', [
                    'error'               => json_last_error_msg(),
                    'raw_content_trimmed' => Str::limit($rawContent, 500)
                    // Log snippet of raw content
                ]);
                // Attempt to provide more context in the exception
                throw new \Exception(
                    'Failed to decode JSON response: ' . json_last_error_msg()
                    . '. Raw content started with: ' . Str::limit(
                        $rawContent,
                        100
                    )
                );
            }

            Log::info('Successfully analyzed document with Google Gemini API', [
                'document_id' => $this->document->id,
                'title'       => $content['title'] ?? 'No title generated'
            ]);

            return $content;
        } catch (\Exception $e) {
            Log::error('Failed to analyze document with Google Gemini API', [
                'document_id' => $this->document->id,
                'error'       => $e->getMessage(),
                'trace'       => $e->getTraceAsString()
                // Optional: include stack trace for debugging
            ]);
            // Re-throw or handle the exception appropriately for your application flow
            throw new \Exception(
                'Failed to analyze document: ' . $e->getMessage()
            );
        }
    }

    /**
     * Analyze document content using Google Gemini Vision API for image-based documents
     *
     * @return array Parsed JSON response with title, summary, and optional parties
     * @throws \Exception If the API call fails or returns invalid data
     */
    private function analyzeDocumentWithGeminiVision(): array
    {
        Log::info('Analyzing document with Google Gemini Vision API', [
            'document_id' => $this->document->id,
            'mime_type' => $this->document->mime_type
        ]);

        $this->document->update(['ingestion_status' => 'summarizing']);

        try {
            // Generate a temporary URL for the document
            $url = Storage::disk('s3')->temporaryUrl(
                $this->document->storage_path,
                now()->addMinutes(10)
            );

            // Get the image data
            $imageData = file_get_contents($url);
            $base64Image = base64_encode($imageData);

            // Prepare the prompt for Gemini Vision
            $prompt = "Please analyze this document and provide:\n"
                . "1. A title and summary of the document. Be specific about the content.\n"
                . "2. Any parties and their addresses mentioned in the document, but only if you are very confident. Ignore addresses or parties that you cannot be sure of. Do not make up your own.\n\n"
                . "Return valid JSON only, in the format:\n"
                . "{\n"
                . "    \"title\": \"...\",\n"
                . "    \"summary\": \"...\"\n"
                . "}\n\n"
                . "Filename: " . $this->document->original_filename;

            // Call the Google Gemini Vision API directly using HTTP client
            $apiKey = env('GOOGLE_GEMINI_API_KEY');
            $apiUrl = 'https://generativelanguage.googleapis.com/v1/models/gemini-1.5-pro-vision:generateContent';

            $response = Http::withHeaders([
                'Content-Type' => 'application/json'
            ])->post($apiUrl . '?key=' . $apiKey, [
                'contents' => [
                    [
                        'parts' => [
                            ['text' => $prompt],
                            [
                                'inline_data' => [
                                    'mime_type' => $this->document->mime_type,
                                    'data' => $base64Image
                                ]
                            ]
                        ]
                    ]
                ],
                'generationConfig' => [
                    'temperature' => 0.4,
                    'topP' => 0.8,
                    'topK' => 40,
                    'maxOutputTokens' => 2048,
                ]
            ]);

            if (!$response->successful()) {
                $errorData = $response->json();
                $errorMessage = isset($errorData['error']['message']) ? $errorData['error']['message'] : 'Unknown error';
                $errorCode = isset($errorData['error']['code']) ? $errorData['error']['code'] : $response->status();

                Log::error('Google Gemini Vision API request failed', [
                    'document_id' => $this->document->id,
                    'status' => $response->status(),
                    'error_code' => $errorCode,
                    'error_message' => $errorMessage,
                    'response' => $errorData
                ]);
                throw new \Exception('Google Gemini Vision API request failed: ' . $errorCode . ' - ' . $errorMessage);
            }

            $responseData = $response->json();

            // Extract the text from the response
            $rawContent = '';
            if (isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
                $rawContent = $responseData['candidates'][0]['content']['parts'][0]['text'];
            } else {
                throw new \Exception('Unexpected response format from Google Gemini Vision API');
            }

            // Remove any triple backticks if they appear (in case the model wraps JSON in code blocks)
            $rawContent = preg_replace('/```(?:json)?(.*?)```/s', '$1', $rawContent);
            $content = json_decode($rawContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('JSON decode error', [
                    'error' => json_last_error_msg(),
                    'raw_content' => $rawContent
                ]);
                throw new \Exception('Failed to decode JSON response: ' . json_last_error_msg());
            }

            Log::info('Successfully analyzed document with Google Gemini Vision API', [
                'document_id' => $this->document->id,
                'title' => $content['title'] ?? 'No title generated'
            ]);

            return $content;

        } catch (\Exception $e) {
            Log::error('Failed to analyze document with Google Gemini Vision API', [
                'document_id' => $this->document->id,
                'error' => $e->getMessage()
            ]);
            throw new \Exception('Failed to analyze document: ' . $e->getMessage());
        }
    }

    private function transcribeAudioFile(): self
    {
        Log::info('Starting audio transcription process', [
            'document_id' => $this->document->id,
            'mime_type' => $this->document->mime_type
        ]);

        $this->document->update(['ingestion_status' => 'processing']);

        // Generate a temporary URL for the file
        $url = Storage::disk('s3')->temporaryUrl(
            $this->document->storage_path,
            now()->addMinutes(10)
        );

        // Download the file to a temporary location
        $tempPath = storage_path('app/temp/' . uniqid() . '.' . pathinfo($this->document->original_filename, PATHINFO_EXTENSION));
        file_put_contents($tempPath, file_get_contents($url));

        Log::info('Transcribing audio file...', [
            'document_id' => $this->document->id,
        ]);
        try {
            // Transcribe using Whisper API
            $response = OpenAI::audio()->transcribe([
                'model' => 'whisper-1',
                'file' => fopen($tempPath, 'r'),
                'response_format' => 'text'
            ]);

            $transcription = $response->text;

            Log::info('Audio transcription completed successfully', [
                'document_id' => $this->document->id,
            ]);

            // Store the transcription
            $this->document->update([
                'transcription' => $transcription,
                'ingestion_status' => 'summarizing' // Use existing status
            ]);

            // Generate title and summary from transcription
            $messages = [
                [
                    'role' => 'system',
                    'content' => 'You are an assistant that analyzes transcriptions. '
                        . 'Generate a concise title and summary for the following transcription. '
                        . 'Return *only* valid JSON in the format {"title": "...", "summary": "..."} '
                        . 'with no extra text, code blocks, or markdown.'
                ],
                [
                    'role' => 'user',
                    'content' => "Here is a transcription from an audio/video file:\n\n"
                        . $transcription
                        . "\n\nAnalyze this transcription, understand its content, then provide the JSON response."
                ],
            ];

            $completion = OpenAI::chat()->create([
                'model' => 'gpt-4.1-mini',
                'messages' => $messages,
                'max_tokens' => 500,
            ]);

            $rawContent = $completion->choices[0]->message->content ?? '';
            $content = json_decode($rawContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('JSON decode error', [
                    'error' => json_last_error_msg(),
                    'raw_content' => $rawContent
                ]);
                throw new \Exception('Failed to decode JSON response: ' . json_last_error_msg());
            }

            // Update document with title and summary
            $this->document->update([
                'title' => $content['title'] ?? null,
                'description' => $content['summary'] ?? null,
                'skip_vector_store' => false // Allow transcriptions to be added to vector store
            ]);

            // Deduct credits for audio transcription and summary
            $this->deductCreditsForAiProcessing('Audio transcription and summary generated');

            Log::info('Generated title and summary for audio transcription', [
                'document_id' => $this->document->id,
                'title' => $content['title'] ?? null,
                'skip_vector_store' => false
            ]);

            // Create a temporary file with the transcription for OpenAI upload
            $transcriptionFilePath = storage_path('app/temp/' . uniqid() . '.txt');
            file_put_contents($transcriptionFilePath, $transcription);

            // Upload transcription to OpenAI
            try {
                Log::info('Uploading transcription to OpenAI', [
                    'document_id' => $this->document->id,
                    'file_size' => filesize($transcriptionFilePath)
                ]);

                $response = OpenAI::files()->upload([
                    'purpose' => 'assistants',
                    'file' => fopen($transcriptionFilePath, 'r'),
                ]);

                // Update document with OpenAI file ID
                $this->document->update(['openai_file_id' => $response->id]);

                Log::info('Successfully uploaded transcription to OpenAI', [
                    'document_id' => $this->document->id,
                    'openai_file_id' => $response->id
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to upload transcription to OpenAI', [
                    'document_id' => $this->document->id,
                    'error' => $e->getMessage()
                ]);
                throw new \Exception('Failed to upload transcription to OpenAI: ' . $e->getMessage());
            } finally {
                // Clean up temporary transcription file
                if (file_exists($transcriptionFilePath)) {
                    unlink($transcriptionFilePath);
                }
            }

        } catch (\Exception $e) {
            Log::error('Failed to transcribe audio file', [
                'document_id' => $this->document->id,
                'error' => $e->getMessage()
            ]);
            throw new \Exception('Failed to transcribe file: ' . $e->getMessage());
        } finally {
            // Clean up temporary file
            if (file_exists($tempPath)) {
                unlink($tempPath);
            }
        }

        return $this;
    }

    /**
     * Convert DOCX document to PDF and analyze it with Google Gemini
     *
     * @return array Parsed JSON response with title, summary, and optional parties
     * @throws \Exception If the conversion or analysis fails
     */
    private function analyzeDocxDocument(): array
    {
        Log::info('Converting DOCX document to PDF for analysis', [
            'document_id' => $this->document->id,
            'mime_type' => $this->document->mime_type
        ]);

        $this->document->update(['ingestion_status' => 'processing']);

        try {
            // 1. Fetch the DOCX file from S3
            $fileContent = Storage::disk('s3')->get(
                $this->document->storage_path
            );
            if ($fileContent === null) {
                throw new \Exception(
                    'Failed to retrieve document content from S3.'
                );
            }

            // 2. Save to a temporary file
            $tempDocxPath = storage_path('app/temp/' . uniqid() . '.docx');
            file_put_contents($tempDocxPath, $fileContent);

            // 3. Convert DOCX to PDF using DocumentConversionService
            $tempDir = dirname($tempDocxPath);
            $tempPdfPath = $tempDir . '/' . pathinfo($tempDocxPath, PATHINFO_FILENAME) . '.pdf';

            Log::info('Converting DOCX to PDF using DocumentConversionService', [
                'document_id' => $this->document->id,
                'docx_path' => $tempDocxPath
            ]);

            $tempPdfPath = $this->conversionService->convertDocxToPdf($tempDocxPath);

            if (!$tempPdfPath || !file_exists($tempPdfPath)) {
                Log::warning('Document conversion failed, falling back to text extraction', [
                    'document_id' => $this->document->id
                ]);

                // Fall back to text extraction
                return $this->analyzeDocxWithTextExtraction($tempDocxPath);
            }

            Log::info('Successfully converted DOCX to PDF', [
                'document_id' => $this->document->id,
                'pdf_path' => $tempPdfPath,
                'pdf_size' => filesize($tempPdfPath)
            ]);

            // 4. Now analyze the PDF with Gemini
            $this->document->update(['ingestion_status' => 'summarizing']);

            // Read the PDF content
            $pdfContent = file_get_contents($tempPdfPath);
            $base64EncodedContent = base64_encode($pdfContent);

            // 5. Prepare the prompt for Gemini
            $promptText = "Please analyze the provided PDF document and return valid JSON only, in the format:\n"
                . "{\n"
                . "    \"title\": \"...\",\n"
                . "    \"summary\": \"...\",\n"
                . "    \"parties\": [\n"
                . "        {\n"
                . "            \"name\": \"...\",\n"
                . "            \"address_line1\": \"...\",\n"
                . "            \"address_line2\": null,\n"
                . "            \"city\": \"...\",\n"
                . "            \"state\": \"...\",\n"
                . "            \"zip\": \"...\",\n"
                . "            \"email\": null,\n"
                . "            \"phone\": null,\n"
                . "            \"relationship\": \"...\" /* one of: attorney, court, opponent, or omit if unsure */\n"
                . "        }\n"
                . "    ]\n"
                . "}\n\n"
                . "For the title and summary, be specific about the content. "
                . "For parties, only include entries where you are confident about the name and address. Ignore uncertain ones. Do not invent information.";

            // 6. Call the Google Gemini API with the PDF
            $apiKey = env('GOOGLE_GEMINI_API_KEY');
            $apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-latest:generateContent';

            $payload = [
                'contents' => [
                    [
                        'parts' => [
                            // Part 1: The text prompt
                            ['text' => $promptText],
                            // Part 2: The PDF data
                            [
                                'inlineData' => [
                                    'mimeType' => 'application/pdf',
                                    'data' => $base64EncodedContent
                                ]
                            ]
                        ]
                    ]
                ],
                'generationConfig' => [
                    'responseMimeType' => 'application/json',
                    'temperature' => 0.4,
                    'topP' => 0.8,
                    'topK' => 40,
                    'maxOutputTokens' => 2048,
                ]
            ];

            // 7. Call the Google Gemini API
            $response = Http::withHeaders([
                'Content-Type' => 'application/json'
            ])->post($apiUrl . '?key=' . $apiKey, $payload);

            // 8. Clean up temporary files regardless of API response
            try {
                if (file_exists($tempDocxPath)) {
                    unlink($tempDocxPath);
                }
                if (file_exists($tempPdfPath)) {
                    unlink($tempPdfPath);
                }
            } catch (\Exception $e) {
                // Just log cleanup errors but don't fail the process
                Log::warning('Failed to clean up temporary files', [
                    'document_id' => $this->document->id,
                    'error' => $e->getMessage()
                ]);
            }

            if (!$response->successful()) {
                $errorData = $response->json();
                $errorMessage = $errorData['error']['message'] ?? 'Unknown error';
                $errorCode = $errorData['error']['code'] ?? $response->status();
                Log::error('Google Gemini API request failed', [
                    'document_id' => $this->document->id,
                    'status' => $response->status(),
                    'error_code' => $errorCode,
                    'error_message' => $errorMessage
                ]);
                throw new \Exception(
                    'Google Gemini API request failed: ' . $errorCode . ' - ' . $errorMessage
                );
            }

            $responseData = $response->json();
            $rawContent = '';

            if (isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
                $rawContent = $responseData['candidates'][0]['content']['parts'][0]['text'];
            } else {
                Log::error(
                    'Unexpected response format from Google Gemini API',
                    ['response' => $responseData]
                );
                throw new \Exception(
                    'Unexpected response format from Google Gemini API'
                );
            }

            // 9. Process the response
            $rawContent = preg_replace(
                '/^```(?:json)?\s*|\s*```$/s',
                '',
                $rawContent
            );

            $content = json_decode($rawContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('JSON decode error', [
                    'error' => json_last_error_msg(),
                    'raw_content_trimmed' => Str::limit($rawContent, 500)
                ]);
                throw new \Exception(
                    'Failed to decode JSON response: ' . json_last_error_msg()
                    . '. Raw content started with: ' . Str::limit(
                        $rawContent,
                        100
                    )
                );
            }

            Log::info('Successfully analyzed converted PDF document', [
                'document_id' => $this->document->id,
                'title' => $content['title'] ?? 'No title generated'
            ]);

            return $content;
        } catch (\Exception $e) {
            // Clean up temporary files in case of exception
            try {
                if (isset($tempDocxPath) && file_exists($tempDocxPath)) {
                    unlink($tempDocxPath);
                }
                if (isset($tempPdfPath) && file_exists($tempPdfPath)) {
                    unlink($tempPdfPath);
                }
            } catch (\Exception $cleanupException) {
                // Just log cleanup errors
                Log::warning('Failed to clean up temporary files during exception handling', [
                    'document_id' => $this->document->id,
                    'error' => $cleanupException->getMessage()
                ]);
            }

            Log::error('Failed to analyze DOCX document', [
                'document_id' => $this->document->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new \Exception(
                'Failed to analyze document: ' . $e->getMessage()
            );
        }
    }



    /**
     * Extract text from DOCX and analyze it with Google Gemini (fallback method)
     *
     * @param string $docxPath Path to the DOCX file
     * @return array Parsed JSON response with title, summary, and optional parties
     * @throws \Exception If the extraction or analysis fails
     */
    private function analyzeDocxWithTextExtraction(string $docxPath): array
    {
        Log::info('Falling back to text extraction for DOCX document', [
            'document_id' => $this->document->id,
            'docx_path' => $docxPath
        ]);

        $this->document->update(['ingestion_status' => 'processing']);

        try {
            // 1. Extract text from the DOCX file
            $extractedText = $this->extractTextFromDocx($docxPath);

            // 2. Prepare for analysis
            $this->document->update(['ingestion_status' => 'summarizing']);

            // 3. Prepare the prompt for Gemini
            $promptText = "Please analyze the provided document text and return valid JSON only, in the format:\n"
                . "{\n"
                . "    \"title\": \"...\",\n"
                . "    \"summary\": \"...\",\n"
                . "    \"parties\": [\n"
                . "        {\n"
                . "            \"name\": \"...\",\n"
                . "            \"address_line1\": \"...\",\n"
                . "            \"address_line2\": null,\n"
                . "            \"city\": \"...\",\n"
                . "            \"state\": \"...\",\n"
                . "            \"zip\": \"...\",\n"
                . "            \"email\": null,\n"
                . "            \"phone\": null,\n"
                . "            \"relationship\": \"...\" /* one of: attorney, court, opponent, or omit if unsure */\n"
                . "        }\n"
                . "    ]\n"
                . "}\n\n"
                . "For the title and summary, be specific about the content. "
                . "For parties, only include entries where you are confident about the name and address. Ignore uncertain ones. Do not invent information.\n\n"
                . "Document text:\n" . $extractedText;

            // 4. Call the Google Gemini API with text-only input
            $apiKey = env('GOOGLE_GEMINI_API_KEY');
            $apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-latest:generateContent';

            $payload = [
                'contents' => [
                    [
                        'parts' => [
                            ['text' => $promptText]
                        ]
                    ]
                ],
                'generationConfig' => [
                    'responseMimeType' => 'application/json',
                    'temperature' => 0.4,
                    'topP' => 0.8,
                    'topK' => 40,
                    'maxOutputTokens' => 2048,
                ]
            ];

            // 5. Call the Google Gemini API
            $response = Http::withHeaders([
                'Content-Type' => 'application/json'
            ])->post($apiUrl . '?key=' . $apiKey, $payload);

            // 6. Clean up the temporary DOCX file
            if (file_exists($docxPath)) {
                unlink($docxPath);
            }

            if (!$response->successful()) {
                $errorData = $response->json();
                $errorMessage = $errorData['error']['message'] ?? 'Unknown error';
                $errorCode = $errorData['error']['code'] ?? $response->status();
                Log::error('Google Gemini API request failed', [
                    'document_id' => $this->document->id,
                    'status' => $response->status(),
                    'error_code' => $errorCode,
                    'error_message' => $errorMessage
                ]);
                throw new \Exception(
                    'Google Gemini API request failed: ' . $errorCode . ' - ' . $errorMessage
                );
            }

            $responseData = $response->json();
            $rawContent = '';

            if (isset($responseData['candidates'][0]['content']['parts'][0]['text'])) {
                $rawContent = $responseData['candidates'][0]['content']['parts'][0]['text'];
            } else {
                Log::error(
                    'Unexpected response format from Google Gemini API',
                    ['response' => $responseData]
                );
                throw new \Exception(
                    'Unexpected response format from Google Gemini API'
                );
            }

            // 7. Process the response
            $rawContent = preg_replace(
                '/^```(?:json)?\s*|\s*```$/s',
                '',
                $rawContent
            );

            $content = json_decode($rawContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('JSON decode error', [
                    'error' => json_last_error_msg(),
                    'raw_content_trimmed' => Str::limit($rawContent, 500)
                ]);
                throw new \Exception(
                    'Failed to decode JSON response: ' . json_last_error_msg()
                    . '. Raw content started with: ' . Str::limit(
                        $rawContent,
                        100
                    )
                );
            }

            Log::info('Successfully analyzed DOCX document using text extraction', [
                'document_id' => $this->document->id,
                'title' => $content['title'] ?? 'No title generated'
            ]);

            return $content;
        } catch (\Exception $e) {
            // Clean up temporary file in case of exception
            if (file_exists($docxPath)) {
                unlink($docxPath);
            }

            Log::error('Failed to analyze DOCX document with text extraction', [
                'document_id' => $this->document->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new \Exception(
                'Failed to analyze document: ' . $e->getMessage()
            );
        }
    }

    /**
     * Extract text from a DOCX file
     *
     * @param string $filePath Path to the DOCX file
     * @return string Extracted text content
     * @throws \Exception If text extraction fails
     */
    private function extractTextFromDocx(string $filePath): string
    {
        try {
            // Method 1: Using ZipArchive to extract the XML content
            $zip = new \ZipArchive();
            $text = '';

            if ($zip->open($filePath) === true) {
                // The main content is in word/document.xml
                if (($index = $zip->locateName('word/document.xml')) !== false) {
                    $content = $zip->getFromIndex($index);
                    $zip->close();

                    // Convert the XML content to text
                    $xml = new \SimpleXMLElement($content);
                    $xml->registerXPathNamespace('w', 'http://schemas.openxmlformats.org/wordprocessingml/2006/main');
                    $paragraphs = $xml->xpath('//w:p');

                    foreach ($paragraphs as $paragraph) {
                        $xmlString = $paragraph->asXML();
                        // Extract text between <w:t> tags
                        preg_match_all('/<w:t[^>]*>(.*?)<\/w:t>/s', $xmlString, $matches);
                        if (!empty($matches[1])) {
                            $text .= implode('', $matches[1]) . "\n";
                        }
                    }
                } else {
                    $zip->close();
                    throw new \Exception('Could not locate document.xml in the DOCX file');
                }
            } else {
                throw new \Exception('Could not open the DOCX file');
            }

            // If we got no text, try method 2 as a fallback
            if (empty(trim($text))) {
                Log::warning('ZipArchive method failed to extract text, trying fallback method', [
                    'document_id' => $this->document->id
                ]);

                // Method 2: Using DocumentConversionService to convert to TXT
                $outputTextFile = storage_path('app/temp/' . uniqid() . '.txt');
                $outputDir = dirname($outputTextFile);

                // Use LibreOffice command from config if available
                $libreOfficeCommand = config('services.document_conversion.libreoffice_command', 'soffice');
                $command = "{$libreOfficeCommand} --headless --convert-to txt:text --outdir " . escapeshellarg($outputDir) . " " . escapeshellarg($filePath);

                exec($command, $output, $returnCode);

                $txtFilename = pathinfo($filePath, PATHINFO_FILENAME) . '.txt';
                $expectedTxtPath = $outputDir . '/' . $txtFilename;

                if ($returnCode === 0 && file_exists($expectedTxtPath)) {
                    $text = file_get_contents($expectedTxtPath);
                    unlink($expectedTxtPath); // Clean up
                } else {
                    Log::warning('Text conversion failed, using basic text extraction', [
                        'document_id' => $this->document->id,
                        'return_code' => $returnCode
                    ]);
                }
            }

            // If both methods failed, return a message
            if (empty(trim($text))) {
                return "[Document text extraction failed. This appears to be a DOCX file but the content could not be extracted.]";
            }

            return $text;
        } catch (\Exception $e) {
            Log::error('Failed to extract text from DOCX', [
                'document_id' => $this->document->id,
                'error' => $e->getMessage()
            ]);
            return "[Document text extraction failed: " . $e->getMessage() . "]";
        }
    }

    private function attachToVectorStore(): self
    {
        // Early return conditions with additional logging
        if ($this->document->skip_vector_store) {
            Log::info('Skipping vector store attachment - skip_vector_store flag is true', [
                'document_id' => $this->document->id,
                'mime_type' => $this->document->mime_type
            ]);
            return $this;
        }

        // Check for any image type (image/*)
        if (str_starts_with($this->document->mime_type, 'image/')) {
            Log::info('Skipping vector store attachment for image file', [
                'document_id' => $this->document->id,
                'mime_type' => $this->document->mime_type
            ]);
            $this->document->update(['skip_vector_store' => true]);
            return $this;
        }

        $this->configureOpenAI();
        $this->document->update(['ingestion_status' => 'indexing']);

        $vectorStoreId = $this->document->caseFile->openai_vector_store_id;

        if (!$vectorStoreId) {
            Log::error('Cannot attach to vector store - case file has no vector store ID', [
                'document_id' => $this->document->id,
                'case_file_id' => $this->document->caseFile->id
            ]);
            throw new \Exception('Case file has no vector store ID');
        }

        if (!$this->document->openai_file_id) {
            Log::error('Cannot attach to vector store - document has no OpenAI file ID', [
                'document_id' => $this->document->id
            ]);
            throw new \Exception('Document has no OpenAI file ID');
        }

        try {
            Log::info('Attaching document to vector store', [
                'document_id' => $this->document->id,
                'vector_store_id' => $vectorStoreId,
                'openai_file_id' => $this->document->openai_file_id,
                'is_audio_transcription' => str_starts_with($this->document->mime_type, 'audio/')
            ]);

            OpenAI::vectorStores()->files()->create($vectorStoreId, [
                'file_id' => $this->document->openai_file_id
            ]);

            Log::info('Successfully attached document to vector store', [
                'document_id' => $this->document->id,
                'vector_store_id' => $vectorStoreId,
                'openai_file_id' => $this->document->openai_file_id
            ]);

            $this->document->update(['ingestion_status' => 'completed']);

            // Deduct credits for vector store indexing
            $this->deductCreditsForAiProcessing('Document indexed in vector database');
        } catch (\Exception $e) {
            Log::error('Failed to attach document to vector store', [
                'document_id' => $this->document->id,
                'vector_store_id' => $vectorStoreId,
                'openai_file_id' => $this->document->openai_file_id,
                'error' => $e->getMessage()
            ]);
            throw new \Exception('Failed to attach document to vector store: ' . $e->getMessage());
        }

        return $this;
    }
}
