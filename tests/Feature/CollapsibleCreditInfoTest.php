<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CollapsibleCreditInfoTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function credits_page_has_collapsible_credit_info_section()
    {
        $user = User::factory()->create();
        
        $response = $this->actingAs($user)
            ->get(route('credits.index'));
            
        $response->assertOk()
            ->assertSee('How Credits Work')
            ->assertSee('onclick="toggleCreditInfo()"', false)
            ->assertSee('id="credit-info-content"', false)
            ->assertSee('class="hidden mt-4"', false);
    }

    /** @test */
    public function subscription_page_has_collapsible_credit_info_section()
    {
        $user = User::factory()->create();
        
        $response = $this->actingAs($user)
            ->get(route('subscriptions.index'));
            
        $response->assertOk()
            ->assertSee('How Credits Work - Click to Learn More')
            ->assertSee('onclick="toggleCreditInfoSubscriptions()"', false)
            ->assertSee('id="credit-info-content-subscriptions"', false)
            ->assertSee('class="hidden mt-4"', false);
    }

    /** @test */
    public function both_pages_have_toggle_javascript_functions()
    {
        $user = User::factory()->create();
        
        // Test credits page
        $creditsResponse = $this->actingAs($user)
            ->get(route('credits.index'));
            
        $creditsResponse->assertOk()
            ->assertSee('function toggleCreditInfo()', false);

        // Test subscriptions page
        $subscriptionsResponse = $this->actingAs($user)
            ->get(route('subscriptions.index'));
            
        $subscriptionsResponse->assertOk()
            ->assertSee('function toggleCreditInfoSubscriptions()', false);
    }

    /** @test */
    public function credit_info_sections_default_to_collapsed()
    {
        $user = User::factory()->create();
        
        // Test credits page - content should be hidden by default
        $creditsResponse = $this->actingAs($user)
            ->get(route('credits.index'));
            
        $creditsResponse->assertOk()
            ->assertSee('id="credit-info-content" class="hidden mt-4"', false);

        // Test subscriptions page - content should be hidden by default
        $subscriptionsResponse = $this->actingAs($user)
            ->get(route('subscriptions.index'));
            
        $subscriptionsResponse->assertOk()
            ->assertSee('id="credit-info-content-subscriptions" class="hidden mt-4"', false);
    }

    /** @test */
    public function welcome_page_has_collapsible_credit_info_section()
    {
        $response = $this->get('/');

        $response->assertOk()
            ->assertSee('How Our Credit System Works - Click to Learn More')
            ->assertSee('onclick="toggleCreditInfoWelcome()"', false)
            ->assertSee('id="credit-info-content-welcome"', false)
            ->assertSee('class="hidden mt-4"', false);
    }

    /** @test */
    public function welcome_page_has_toggle_javascript_function()
    {
        $response = $this->get('/');

        $response->assertOk()
            ->assertSee('function toggleCreditInfoWelcome()', false);
    }

    /** @test */
    public function welcome_page_credit_info_defaults_to_collapsed()
    {
        $response = $this->get('/');

        $response->assertOk()
            ->assertSee('id="credit-info-content-welcome" class="hidden mt-4"', false);
    }
}
